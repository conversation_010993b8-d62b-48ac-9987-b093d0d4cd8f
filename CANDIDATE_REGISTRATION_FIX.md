# Candidate Registration System Fix

## Problem
The IELTS candidate registration system was preventing existing candidates from registering for new test dates due to unique constraints on email and passport number fields. This caused "email address already exists" errors when candidates tried to register for multiple tests.

## Solution Overview
Modified the system to allow existing candidates to register for multiple test dates while preventing duplicate registrations for the same test date.

## Changes Made

### 1. Database Schema Updates (`src/lib/db/schema.ts`)
- **Removed** unique constraints on `email` and `passportNumber` fields
- **Added** composite unique constraints:
  - `uniqueCandidateTestDate`: prevents same email from registering for same test date
  - `uniquePassportTestDate`: prevents same passport number from registering for same test date

### 2. Database Migration (`drizzle/0003_silent_baron_zemo.sql`)
```sql
ALTER TABLE "candidates" DROP CONSTRAINT "candidates_email_unique";
ALTER TABLE "candidates" DROP CONSTRAINT "candidates_passport_number_unique";
ALTER TABLE "candidates" ADD CONSTRAINT "candidates_email_test_date_unique" UNIQUE("email","test_date");
ALTER TABLE "candidates" ADD CONSTRAINT "candidates_passport_number_test_date_unique" UNIQUE("passport_number","test_date");
```

### 3. API Endpoint Updates (`src/app/api/admin/candidates/route.ts`)
- **Enhanced POST endpoint** to handle existing candidate registrations
- **Added logic** to detect existing candidates by email or passport number
- **Implemented** candidate number reuse for existing candidates
- **Updated** error handling for new constraint violations
- **Added** validation to prevent duplicate registrations for same test date

### 4. Search API Enhancement (`src/app/api/admin/candidates/search/route.ts`)
- **Extended search** to work with both email addresses and passport/birth certificate numbers
- **Added** candidate grouping to show unique candidates with their test history
- **Improved** search results to display test registrations per candidate

### 5. Frontend Form Updates (`src/app/admin/candidates/new/page.tsx`)
- **Added** email-based candidate search functionality
- **Enhanced** existing candidate detection and selection
- **Improved** user interface messaging for existing vs new candidates
- **Added** search indicators for both email and passport fields
- **Updated** button text to show "Register for Test" vs "Create Candidate"

## Key Features

### ✅ Multi-Test Registration Support
- Existing candidates can register for multiple test dates
- Same email/passport can be used for different test dates
- Prevents duplicate registrations for the same test date

### ✅ Enhanced Search Functionality
- Search by email address or passport/birth certificate number
- Real-time candidate detection during form entry
- Visual indicators for existing candidate selection

### ✅ Improved User Experience
- Clear messaging for existing vs new candidate registration
- Pre-filled forms for existing candidates
- Dynamic button text based on registration type
- Better error messages for constraint violations

### ✅ Data Integrity
- Composite unique constraints prevent true duplicates
- Candidate number reuse for existing candidates
- Proper validation and error handling

## Usage Examples

### Scenario 1: New Candidate Registration
1. Admin enters new candidate details
2. System creates new candidate with auto-generated candidate number
3. Success message: "Candidate created successfully"

### Scenario 2: Existing Candidate - New Test Registration
1. Admin enters email or passport number of existing candidate
2. System detects existing candidate and shows selection options
3. Admin selects existing candidate
4. Form pre-fills with existing candidate data
5. Admin selects new test date
6. System creates new test registration with same candidate number
7. Success message: "Candidate registered for new test"

### Scenario 3: Duplicate Registration Prevention
1. Admin tries to register existing candidate for same test date
2. System shows error: "Candidate is already registered for this test date"
3. Registration is prevented

## Technical Benefits
- **Scalable**: Supports unlimited test registrations per candidate
- **Consistent**: Maintains data integrity with proper constraints
- **User-friendly**: Intuitive interface for both new and existing candidates
- **Robust**: Comprehensive error handling and validation
- **Efficient**: Optimized search and candidate detection

## Migration Required
Run the database migration to update constraints:
```bash
npm run db:generate  # Already completed
npm run db:migrate   # Apply when ready
```

The system is now ready to handle existing candidates registering for multiple tests while maintaining data integrity and preventing true duplicates.
