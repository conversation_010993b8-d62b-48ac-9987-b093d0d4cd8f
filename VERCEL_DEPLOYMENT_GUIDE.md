# IELTS Certification System - Vercel Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the IELTS Certification System to Vercel with minimal changes to the existing codebase.

## Prerequisites

- Node.js 18+ installed
- Vercel CLI installed (`npm i -g vercel`)
- PostgreSQL database (Neon, Supabase, or other cloud provider)
- Anthropic Claude API key

## Environment Variables Required

The following environment variables must be configured in Vercel:

### Core Application
```env
NEXTAUTH_SECRET=your-production-secret-key-here
NEXTAUTH_URL=https://your-app-name.vercel.app
DATABASE_URL=your-postgresql-connection-string
```

### AI Integration
```env
ANTHROPIC_API_KEY=your-claude-api-key
```

### Demo Credentials (Hardcoded for easier deployment)
```env
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
```

### Public URLs
```env
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
```

## Deployment Steps

### Step 1: Prepare the Project

1. **Ensure build passes locally:**
   ```bash
   npm run build
   ```

2. **Verify all TypeScript errors are resolved** (already done in this preparation)

### Step 2: Configure Vercel Project

1. **Login to Vercel:**
   ```bash
   npx vercel login
   ```

2. **Initialize Vercel project:**
   ```bash
   npx vercel
   ```
   - Choose "Link to existing project" or "Create new project"
   - Follow the prompts to configure your project

### Step 3: Set Environment Variables

Use the Vercel CLI to set environment variables:

```bash
# Core application variables
npx vercel env add NEXTAUTH_SECRET production
npx vercel env add NEXTAUTH_URL production
npx vercel env add DATABASE_URL production

# AI integration
npx vercel env add ANTHROPIC_API_KEY production

# Demo credentials
npx vercel env add ADMIN_EMAIL production
npx vercel env add ADMIN_PASSWORD production

# Public URL
npx vercel env add NEXT_PUBLIC_APP_URL production
```

### Step 4: Deploy

1. **Deploy to production:**
   ```bash
   npx vercel --prod
   ```

2. **Verify deployment:**
   - Check the deployment URL provided by Vercel
   - Test authentication with demo credentials
   - Verify database connectivity

## Configuration Files Added/Modified

### 1. `vercel.json` (Added)
```json
{
  "functions": {
    "src/app/api/**/*.ts": {
      "runtime": "nodejs20.x"
    }
  },
  "regions": ["iad1"],
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXTAUTH_URL": "@nextauth_url",
    "ANTHROPIC_API_KEY": "@anthropic_api_key",
    "ADMIN_EMAIL": "@admin_email",
    "ADMIN_PASSWORD": "@admin_password",
    "NEXT_PUBLIC_APP_URL": "@next_public_app_url"
  }
}
```

### 2. `next.config.ts` (Modified)
- Added `serverExternalPackages` for `postgres` and `bcryptjs`
- Added webpack configuration to handle Node.js modules
- Removed deprecated experimental configuration

## Database Setup

### Option 1: Use Existing Neon Database
The project is already configured with a Neon PostgreSQL database. The existing `DATABASE_URL` can be used for production.

### Option 2: Set Up New Database
If you prefer a new database:

1. **Create a PostgreSQL database** (Neon, Supabase, Railway, etc.)
2. **Update the DATABASE_URL** environment variable
3. **Run migrations after deployment:**
   ```bash
   # Connect to your Vercel project
   npx vercel env pull .env.production
   
   # Run migrations locally against production DB
   npm run db:migrate
   
   # Set up initial users
   npm run db:setup
   ```

## Demo Credentials

The system includes hardcoded demo credentials for easier deployment:

- **Admin**: <EMAIL> / admin123
- **Test Checker**: <EMAIL> / checker123

These are automatically created during database setup.

## Features Verified for Deployment

✅ **Build Process**: Successfully compiles without errors
✅ **TypeScript**: All type errors resolved
✅ **Authentication**: NextAuth.js configured for production
✅ **Database**: PostgreSQL connection and schema ready
✅ **API Routes**: All endpoints configured for serverless deployment
✅ **Static Assets**: Properly configured for Vercel CDN
✅ **Environment Variables**: Properly configured for production

## Post-Deployment Verification

After deployment, verify the following:

1. **Homepage loads** at your Vercel URL
2. **Authentication works** with demo credentials
3. **Database connectivity** is functional
4. **Search functionality** works on public pages
5. **Admin dashboard** is accessible
6. **Test checker dashboard** is accessible

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify DATABASE_URL is correctly set
   - Ensure database allows connections from Vercel IPs
   - Check if database is running and accessible

2. **Authentication Issues**
   - Verify NEXTAUTH_SECRET is set
   - Ensure NEXTAUTH_URL matches your Vercel domain
   - Check that demo users exist in database

3. **Build Failures**
   - Run `npm run build` locally to identify issues
   - Check Vercel build logs for specific errors
   - Verify all dependencies are properly installed

### Support

For deployment issues:
1. Check Vercel deployment logs
2. Verify environment variables are set correctly
3. Test database connectivity
4. Review the application logs in Vercel dashboard

## Security Notes

- The demo credentials are hardcoded for easier deployment
- In a production environment, consider implementing proper user management
- Ensure your DATABASE_URL and API keys are kept secure
- Consider enabling Vercel's security features like DDoS protection

## Next Steps After Deployment

1. **Custom Domain**: Configure a custom domain in Vercel dashboard
2. **SSL Certificate**: Automatically provided by Vercel
3. **Monitoring**: Set up Vercel Analytics and monitoring
4. **Backup**: Implement database backup strategy
5. **User Management**: Consider implementing proper user registration/management
