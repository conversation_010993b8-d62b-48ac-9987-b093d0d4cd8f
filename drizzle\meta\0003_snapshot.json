{"id": "aa52fc7b-167c-48df-aa71-297186568e7f", "prevId": "a5b93b3d-c4ee-4208-a0fc-7a9117ec86b7", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_feedback": {"name": "ai_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "test_result_id": {"name": "test_result_id", "type": "text", "primaryKey": false, "notNull": true}, "listening_feedback": {"name": "listening_feedback", "type": "text", "primaryKey": false, "notNull": false}, "reading_feedback": {"name": "reading_feedback", "type": "text", "primaryKey": false, "notNull": false}, "writing_feedback": {"name": "writing_feedback", "type": "text", "primaryKey": false, "notNull": false}, "speaking_feedback": {"name": "speaking_feedback", "type": "text", "primaryKey": false, "notNull": false}, "overall_feedback": {"name": "overall_feedback", "type": "text", "primaryKey": false, "notNull": false}, "study_recommendations": {"name": "study_recommendations", "type": "text", "primaryKey": false, "notNull": false}, "strengths": {"name": "strengths", "type": "json", "primaryKey": false, "notNull": false}, "weaknesses": {"name": "weaknesses", "type": "json", "primaryKey": false, "notNull": false}, "study_plan": {"name": "study_plan", "type": "json", "primaryKey": false, "notNull": false}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ai_feedback_test_result_id_test_results_id_fk": {"name": "ai_feedback_test_result_id_test_results_id_fk", "tableFrom": "ai_feedback", "tableTo": "test_results", "columnsFrom": ["test_result_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.candidates": {"name": "candidates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "candidate_number": {"name": "candidate_number", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": true}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": true}, "passport_number": {"name": "passport_number", "type": "text", "primaryKey": false, "notNull": true}, "test_date": {"name": "test_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "test_center": {"name": "test_center", "type": "text", "primaryKey": false, "notNull": true}, "photo_url": {"name": "photo_url", "type": "text", "primaryKey": false, "notNull": false}, "photo_data": {"name": "photo_data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"candidates_candidate_number_unique": {"name": "candidates_candidate_number_unique", "nullsNotDistinct": false, "columns": ["candidate_number"]}, "candidates_email_test_date_unique": {"name": "candidates_email_test_date_unique", "nullsNotDistinct": false, "columns": ["email", "test_date"]}, "candidates_passport_number_test_date_unique": {"name": "candidates_passport_number_test_date_unique", "nullsNotDistinct": false, "columns": ["passport_number", "test_date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_results": {"name": "test_results", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "listening_score": {"name": "listening_score", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "listening_band_score": {"name": "listening_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "reading_score": {"name": "reading_score", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "reading_band_score": {"name": "reading_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_task1_score": {"name": "writing_task1_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_task2_score": {"name": "writing_task2_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_band_score": {"name": "writing_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_fluency_score": {"name": "speaking_fluency_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_lexical_score": {"name": "speaking_lexical_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_grammar_score": {"name": "speaking_grammar_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_pronunciation_score": {"name": "speaking_pronunciation_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_band_score": {"name": "speaking_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "overall_band_score": {"name": "overall_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "entered_by": {"name": "entered_by", "type": "text", "primaryKey": false, "notNull": false}, "verified_by": {"name": "verified_by", "type": "text", "primaryKey": false, "notNull": false}, "certificate_generated": {"name": "certificate_generated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "certificate_serial": {"name": "certificate_serial", "type": "text", "primaryKey": false, "notNull": false}, "certificate_url": {"name": "certificate_url", "type": "text", "primaryKey": false, "notNull": false}, "ai_feedback_generated": {"name": "ai_feedback_generated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "test_date": {"name": "test_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_results_candidate_id_candidates_id_fk": {"name": "test_results_candidate_id_candidates_id_fk", "tableFrom": "test_results", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_results_entered_by_users_id_fk": {"name": "test_results_entered_by_users_id_fk", "tableFrom": "test_results", "tableTo": "users", "columnsFrom": ["entered_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_results_verified_by_users_id_fk": {"name": "test_results_verified_by_users_id_fk", "tableFrom": "test_results", "tableTo": "users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"test_results_certificate_serial_unique": {"name": "test_results_certificate_serial_unique", "nullsNotDistinct": false, "columns": ["certificate_serial"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'test_checker'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verificationTokens": {"name": "verificationTokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}