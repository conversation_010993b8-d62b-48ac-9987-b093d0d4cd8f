## Long-term Maintainability Features

### Documentation Standards
- **API Documentation**: Auto-generated with TypeScript types
- **Component Documentation**: JSDoc comments for all components
- **Database Documentation**: Schema documentation with relationships
- **Deployment Guide**: Step-by-step deployment instructions
- **Development Setup**: Local development environment setup

### Code Quality Assurance
```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run lint:fix
npm run format
npm run type-check
npm run test -- --run
```

### Monitoring & Health Checks
```typescript
// src/app/api/health/route.ts
import { db } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Database health check
    await db.execute('SELECT 1');

    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      { status: 'unhealthy', error: 'Database connection failed' },
      { status: 503 }
    );
  }
}
```

### Environment Management
```bash
# .env.example - Template for all environments
# Database
DATABASE_URL# IELTS Certification System - Complete Project Requirements

## Project Overview
Build a fast, lightweight IELTS certification management system using Next.js and PostgreSQL that handles candidate registration, result management, AI-powered feedback, and official certificate generation.

## Tech Stack Requirements (Latest Stable Versions)
- **Deployment**: Vercel (with serverless functions)
- **Frontend**: Next.js 15+ (App Router, React 19)
- **Database**: PostgreSQL (Neon) with Drizzle ORM
- **Authentication**: Auth.js v5 (NextAuth.js successor)
- **Styling**: Tailwind CSS v4 + shadcn/ui components
- **File Upload**: Vercel Blob + @vercel/blob
- **PDF Generation**: Puppeteer v23+ with chrome-aws-lambda
- **AI Integration**: Anthropic SDK v0.27+
- **Image Handling**: Next.js Image with Vercel optimization
- **Validation**: Zod v3.22+
- **State Management**: Zustand v4.4+ (for complex client state)
- **Database Client**: @vercel/postgres + Drizzle ORM
- **Type Safety**: TypeScript 5.3+
- **Testing**: Vitest + Playwright (for E2E)
- **Code Quality**: ESLint 9+ + Prettier + Husky

## Modern Database Schema (Drizzle ORM)

### Schema Definition (`src/lib/db/schema.ts`)
```typescript
import { pgTable, serial, varchar, text, timestamp, integer, decimal, boolean, pgEnum, date } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Enums
export const userRoleEnum = pgEnum('user_role', ['admin', 'test_checker']);
export const documentTypeEnum = pgEnum('document_type', ['passport', 'national_id', 'driving_license', 'other']);

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  role: userRoleEnum('role').notNull(),
  emailVerified: timestamp('email_verified'),
  image: varchar('image', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Candidates table
export const candidates = pgTable('candidates', {
  id: serial('id').primaryKey(),
  fullName: varchar('full_name', { length: 255 }).notNull(),
  documentType: documentTypeEnum('document_type').notNull(),
  documentNumber: varchar('document_number', { length: 100 }).notNull(),
  nationality: varchar('nationality', { length: 100 }),
  dateOfBirth: date('date_of_birth'),
  photoUrl: varchar('photo_url', { length: 500 }),
  registrationDate: timestamp('registration_date').defaultNow().notNull(),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Test results table
export const testResults = pgTable('test_results', {
  id: serial('id').primaryKey(),
  candidateId: integer('candidate_id').references(() => candidates.id).notNull(),
  testDate: date('test_date').notNull(),
  listeningScore: decimal('listening_score', { precision: 3, scale: 1 }),
  readingScore: decimal('reading_score', { precision: 3, scale: 1 }),
  writingTask1Score: decimal('writing_task1_score', { precision: 2, scale: 1 }),
  writingTask2Score: decimal('writing_task2_score', { precision: 2, scale: 1 }),
  speakingScore: decimal('speaking_score', { precision: 2, scale: 1 }),
  overallBandScore: decimal('overall_band_score', { precision: 2, scale: 1 }),
  certificateSerial: varchar('certificate_serial', { length: 20 }).unique(),
  aiFeedbackGenerated: boolean('ai_feedback_generated').default(false),
  enteredBy: integer('entered_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// AI feedback table
export const aiFeedback = pgTable('ai_feedback', {
  id: serial('id').primaryKey(),
  testResultId: integer('test_result_id').references(() => testResults.id).notNull(),
  listeningFeedback: text('listening_feedback'),
  readingFeedback: text('reading_feedback'),
  writingFeedback: text('writing_feedback'),
  speakingFeedback: text('speaking_feedback'),
  overallFeedback: text('overall_feedback'),
  studyRecommendations: text('study_recommendations'),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  candidatesCreated: many(candidates),
  resultsEntered: many(testResults),
}));

export const candidatesRelations = relations(candidates, ({ one, many }) => ({
  createdByUser: one(users, {
    fields: [candidates.createdBy],
    references: [users.id],
  }),
  testResults: many(testResults),
}));

export const testResultsRelations = relations(testResults, ({ one }) => ({
  candidate: one(candidates, {
    fields: [testResults.candidateId],
    references: [candidates.id],
  }),
  enteredByUser: one(users, {
    fields: [testResults.enteredBy],
    references: [users.id],
  }),
  aiFeedback: one(aiFeedback),
}));

export const aiFeedbackRelations = relations(aiFeedback, ({ one }) => ({
  testResult: one(testResults, {
    fields: [aiFeedback.testResultId],
    references: [testResults.id],
  }),
}));
```

### Database Configuration (`src/lib/db/index.ts`)
```typescript
import { drizzle } from 'drizzle-orm/vercel-postgres';
import { sql } from '@vercel/postgres';
import * as schema from './schema';

export const db = drizzle(sql, { schema });

export type Database = typeof db;
export * from './schema';
```

## Core Features & Pages

### 1. Authentication System
- **Login Page** (`/login`): NextAuth.js integration
- **Role-based Access Control**: Admin and Test Checker roles
- **Session Management**: Secure session handling

### 2. Admin Dashboard (`/admin`)
**Performance Requirements**:
- Paginated candidate list (20 per page)
- Search by name/document number with debounced input
- Lazy loading for photos

**Features**:
- Register new candidates with photo upload
- View all candidates in optimized table
- Quick search functionality
- Bulk operations support

### 3. Test Checker Dashboard (`/checker`)
**Performance Requirements**:
- Efficient candidate lookup
- Batch result entry capabilities
- Real-time band score calculation

**Features**:
- Search candidates by document number
- Enter test results with automatic validation
- Generate certificate serial numbers
- Trigger AI feedback generation

### 4. Results Search Page (`/search`)
**Public Access - No Authentication Required**
**Performance Requirements**:
- Fast document number lookup with indexing
- Minimal data transfer
- CDN-optimized certificate delivery

**Features**:
- Search by document number
- Display basic result summary
- Link to detailed results page

### 5. Detailed Results Page (`/results/[id]`)
**Features**:
- Complete score breakdown
- AI-generated feedback and recommendations
- Certificate download functionality
- Performance comparison charts
- Study recommendations

### 6. Certificate Generation
**Requirements**:
- Identical to official IELTS certificate design
- Unique serial number generation
- PDF download functionality
- Verification system via serial number
- Candidate photo integration

## Vercel-Specific Optimizations

### Serverless Function Configuration
```javascript
// vercel.json
{
  "functions": {
    "src/app/api/feedback/generate/route.ts": {
      "maxDuration": 30
    },
    "src/app/api/certificate/[serial]/route.ts": {
      "maxDuration": 15
    }
  },
  "crons": [
    {
      "path": "/api/cleanup",
      "schedule": "0 2 * * *"
    }
  ]
}
```

### File Storage Strategy
- **Candidate Photos**: Vercel Blob Storage with automatic optimization
- **Generated Certificates**: Temporary generation with download links
- **File Size Limits**: 4.5MB per photo (Vercel limit compliance)

### Database Connection Management
```typescript
// lib/db.ts - Optimized for Vercel serverless
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 1, // Vercel serverless optimization
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### Edge Runtime Optimization
```typescript
// AI feedback route - Edge runtime for faster cold starts
export const runtime = 'edge';
export const dynamic = 'force-dynamic';
```

## Performance Optimization Strategies
```sql
-- Essential Indexes
CREATE INDEX idx_candidates_document ON candidates(document_number);
CREATE INDEX idx_test_results_candidate ON test_results(candidate_id);
CREATE INDEX idx_test_results_serial ON test_results(certificate_serial);
CREATE INDEX idx_candidates_name ON candidates(full_name);
```

### Application-Level Optimizations
1. **Serverless-First Design**: All API routes optimized for Vercel Functions
2. **Edge Runtime**: Use Edge Runtime for lightweight operations
3. **Static Generation**: Pre-generate public pages where possible
4. **Image Optimization**: Vercel's automatic image optimization
5. **Database Connection Pooling**: Single connection per serverless function
6. **Caching Strategy**: Vercel KV for frequently accessed data
7. **Bundle Optimization**: Tree-shaking and code splitting
8. **Cold Start Minimization**: Lightweight dependencies and edge runtime

### Vercel-Specific File Handling
```typescript
// Photo upload with Vercel Blob
import { put } from '@vercel/blob';

export async function uploadCandidatePhoto(file: File, candidateId: string) {
  const blob = await put(`candidates/${candidateId}/photo.jpg`, file, {
    access: 'public',
    handleUploadUrl: '/api/upload',
  });
  return blob.url;
}
```

## AI Integration Specifications

### Claude 4.0 Sonnet Integration
**API Endpoint**: Use provided Claude API credentials
**Feedback Generation Prompt**:
```
Analyze this IELTS test result and provide detailed feedback:
- Listening: {score}/40
- Reading: {score}/40
- Writing Task 1: {score}/9
- Writing Task 2: {score}/9
- Speaking: {score}/9
- Overall Band: {calculated_band}

Provide:
1. Individual module analysis with strengths and weaknesses
2. Specific study recommendations for each weak area
3. Overall improvement strategy
4. Realistic timeline for score improvement
5. Recommended study materials and techniques
```

### Feedback Categories
- **Module-Specific**: Targeted advice for each skill
- **Overall Strategy**: Holistic improvement approach
- **Study Resources**: Specific book/online recommendations
- **Practice Schedule**: Customized study timeline
- **Weakness Analysis**: Detailed breakdown of problem areas

## Security Requirements

### Data Protection
- Secure file upload with type validation
- Photo storage with access controls
- Database connection encryption (SSL)
- Input sanitization and validation
- Role-based access control

### Certificate Security
- Unique serial number generation (format: IELTS-YYYY-NNNNNN)
- Certificate verification endpoint
- Tamper-proof PDF generation
- Digital signature implementation

## Project Structure (Enterprise-Ready)
```
/
├── .github/
│   └── workflows/
│       ├── ci.yml                    # Continuous Integration
│       └── deploy.yml                # Deployment workflow
├── docs/
│   ├── api/                          # API documentation
│   ├── deployment.md                 # Deployment guide
│   └── development.md                # Development setup
├── scripts/
│   ├── seed.ts                       # Database seeding
│   ├── migrate.ts                    # Migration runner
│   └── backup.ts                     # Database backup
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   └── login/
│   │   │       └── page.tsx
│   │   ├── (dashboard)/
│   │   │   ├── admin/
│   │   │   │   ├── page.tsx          # Admin dashboard
│   │   │   │   ├── candidates/
│   │   │   │   │   ├── page.tsx      # Candidates list
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx  # Register candidate
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── page.tsx  # Candidate detail
│   │   │   │   └── settings/
│   │   │   │       └── page.tsx      # System settings
│   │   │   └── checker/
│   │   │       ├── page.tsx          # Test checker dashboard
│   │   │       ├── results/
│   │   │       │   ├── page.tsx      # Results entry
│   │   │       │   └── [id]/
│   │   │       │       └── page.tsx  # Edit results
│   │   │       └── search/
│   │   │           └── page.tsx      # Search candidates
│   │   ├── (public)/
│   │   │   ├── search/
│   │   │   │   └── page.tsx          # Public search
│   │   │   ├── results/
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx      # Detailed results
│   │   │   └── verify/
│   │   │       └── [serial]/
│   │   │           └── page.tsx      # Certificate verification
│   │   ├── api/
│   │   │   ├── auth/
│   │   │   │   └── [...nextauth]/
│   │   │   │       └── route.ts      # Auth.js configuration
│   │   │   ├── candidates/
│   │   │   │   ├── route.ts          # CRUD operations
│   │   │   │   ├── [id]/
│   │   │   │   │   └── route.ts      # Individual candidate
│   │   │   │   └── search/
│   │   │   │       └── route.ts      # Search endpoint
│   │   │   ├── results/
│   │   │   │   ├── route.ts          # Results CRUD
│   │   │   │   └── [id]/
│   │   │   │       ├── route.ts      # Individual result
│   │   │   │       └── feedback/
│   │   │   │           └── route.ts  # AI feedback
│   │   │   ├── certificates/
│   │   │   │   ├── [serial]/
│   │   │   │   │   └── route.ts      # Download/verify
│   │   │   │   └── generate/
│   │   │   │       └── route.ts      # Generate certificate
│   │   │   ├── upload/
│   │   │   │   └── route.ts          # File upload handler
│   │   │   └── health/
│   │   │       └── route.ts          # Health check
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   ├── loading.tsx               # Global loading UI
│   │   ├── error.tsx                 # Global error boundary
│   │   └── not-found.tsx             # 404 page
│   ├── components/
│   │   ├── ui/                       # shadcn/ui components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── table.tsx
│   │   │   └── toast.tsx
│   │   ├── forms/                    # Form components
│   │   │   ├── candidate-form.tsx
│   │   │   ├── results-form.tsx
│   │   │   └── search-form.tsx
│   │   ├── layouts/                  # Layout components
│   │   │   ├── dashboard-layout.tsx
│   │   │   ├── auth-layout.tsx
│   │   │   └── public-layout.tsx
│   │   ├── charts/                   # Data visualization
│   │   │   ├── results-chart.tsx
│   │   │   └── performance-chart.tsx
│   │   └── features/                 # Feature-specific components
│   │       ├── candidates/
│   │       ├── results/
│   │       └── certificates/
│   ├── lib/
│   │   ├── auth/
│   │   │   ├── config.ts             # Auth.js configuration
│   │   │   └── middleware.ts         # Auth middleware
│   │   ├── db/
│   │   │   ├── index.ts              # Database client
│   │   │   ├── schema.ts             # Drizzle schema
│   │   │   └── migrations/           # Database migrations
│   │   ├── services/                 # Business logic
│   │   │   ├── candidates.ts
│   │   │   ├── results.ts
│   │   │   ├── certificates.ts
│   │   │   └── ai-feedback.ts
│   │   ├── utils/
│   │   │   ├── validations.ts        # Zod schemas
│   │   │   ├── calculations.ts       # IELTS band calculations
│   │   │   ├── file-upload.ts        # File handling
│   │   │   └── pdf-generator.ts      # PDF generation
│   │   ├── hooks/                    # Custom React hooks
│   │   │   ├── use-candidates.ts
│   │   │   ├── use-results.ts
│   │   │   └── use-feedback.ts
│   │   └── constants/
│   │       ├── routes.ts             # Application routes
│   │       ├── band-scores.ts        # IELTS scoring tables
│   │       └── validations.ts        # Validation constants
│   ├── stores/                       # Zustand stores
│   │   ├── candidates.ts
│   │   ├── results.ts
│   │   └── ui.ts
│   └── types/
│       ├── auth.ts                   # Authentication types
│       ├── database.ts               # Database types
│       ├── api.ts                    # API response types
│       └── index.ts                  # Common types
├── tests/
│   ├── __mocks__/                    # Test mocks
│   ├── e2e/                          # Playwright tests
│   ├── unit/                         # Unit tests
│   └── setup.ts                      # Test setup
├── .env.example                      # Environment variables template
├── .env.local                        # Local environment (gitignored)
├── .gitignore
├── .eslintrc.json                    # ESLint configuration
├── .prettierrc                       # Prettier configuration
├── drizzle.config.ts                 # Drizzle configuration
├── next.config.js                    # Next.js configuration
├── tailwind.config.ts                # Tailwind configuration
├── tsconfig.json                     # TypeScript configuration
├── vercel.json                       # Vercel configuration
├── vitest.config.ts                  # Vitest configuration
├── playwright.config.ts              # Playwright configuration
├── package.json
└── README.md
```

## API Endpoints

### Authentication
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout

### Candidates
- `GET /api/candidates` - List candidates (paginated)
- `POST /api/candidates` - Register new candidate
- `GET /api/candidates/search` - Search candidates

### Results
- `POST /api/results` - Enter test results
- `GET /api/results/[id]` - Get detailed results
- `GET /api/search` - Public result search

### AI Feedback
- `POST /api/feedback/generate` - Generate AI feedback
- `GET /api/feedback/[resultId]` - Get generated feedback

### Certificates
- `GET /api/certificate/[serial]` - Download certificate PDF
- `GET /api/certificate/verify/[serial]` - Verify certificate

## Development Priorities

### Phase 1: Core Infrastructure
1. Database setup and migrations
2. Authentication system
3. Basic CRUD operations
4. Admin dashboard with candidate registration

### Phase 2: Result Management
1. Test checker interface
2. Result entry system
3. Automatic band score calculation
4. Basic search functionality

### Phase 3: AI & Certificates
1. Claude API integration
2. AI feedback generation
3. Certificate design and PDF generation
4. Serial number system

### Phase 4: Optimization & Polish
1. Performance optimizations
2. Advanced search features
3. Detailed analytics
4. UI/UX improvements

## Performance Benchmarks
- **Page Load Time**: < 2 seconds
- **Database Queries**: < 100ms average
- **Search Results**: < 500ms
- **PDF Generation**: < 3 seconds
- **AI Feedback**: < 10 seconds

## Current Implementation Status

### ✅ Completed Features
1. **Project Setup**: Next.js 15, TypeScript, Tailwind CSS, Drizzle ORM
2. **Database Schema**: Complete schema with users, candidates, test results, AI feedback
3. **Authentication**: NextAuth.js setup with credentials provider and middleware
4. **Home Page**: Landing page with navigation and feature overview
5. **Search Page**: Public search functionality for test results with API endpoint
6. **Authentication Pages**: Complete signin page with role-based redirects
7. **Admin Dashboard**:
   - Dashboard layout with navigation
   - Main dashboard with statistics
   - Candidates management page with search/filter
   - Add candidate form with validation
   - API endpoints for dashboard stats and candidate CRUD
8. **Test Checker Dashboard**:
   - Dashboard layout with navigation
   - Main dashboard with checker-specific stats
   - Candidate search functionality with multiple search types
   - Test results entry form with comprehensive scoring
   - Results list view with filtering and status management
   - Individual result detail view with complete score breakdown
   - API endpoints for all checker operations
9. **AI Feedback System**:
   - AI feedback generation page with result selection
   - Integration with Anthropic Claude API
   - Comprehensive feedback including strengths, improvements, recommendations
   - Fallback mock feedback system
   - Save and retrieve feedback functionality
10. **Environment Configuration**: .env.example and setup scripts
11. **Database Setup**: Migration scripts and initial user creation
12. **Documentation**: Comprehensive README with setup instructions

13. **Certificate Generation System**:
    - PDF certificate generation with jsPDF integration
    - Automatic certificate creation from test results
    - Download functionality with proper file naming
    - Certificate status tracking in database
14. **File Upload System**:
    - Reusable FileUpload component with drag-and-drop
    - Photo upload for candidate registration
    - File validation and size limits
    - Secure file storage and management
15. **Results Editing System**:
    - Complete edit form for existing test results
    - Status management (pending, completed, verified)
    - Automatic overall band score recalculation
    - Form validation and error handling
16. **Advanced Search & Filtering**:
    - Multi-criteria search with text and filters
    - Date range filtering for test dates
    - Band score range filtering
    - Test center and nationality filters
    - Result status and availability filters
17. **Data Export System**:
    - CSV export functionality for search results
    - Comprehensive data export with all fields
    - Filtered export based on search criteria
    - Proper file formatting and headers

### ✅ Recently Completed Features (Phase 1 & 2)
18. **Public Results Page**: Complete detailed results page at `/results/[id]` with:
    - Complete score breakdown for all IELTS modules
    - AI-generated feedback display
    - Professional, mobile-responsive layout
    - Certificate download functionality
    - Performance visualization with charts
19. **Public API Endpoints**: Public access endpoints without authentication:
    - `GET /api/results/[id]` - Public access to detailed test results
    - `GET /api/feedback/[resultId]` - Public access to AI feedback
    - Proper error handling and data validation
20. **Certificate Verification System**: Complete verification system with:
    - Verification page at `/verify/[serial]` and `/verify`
    - API endpoint `/api/certificate/verify/[serial]`
    - Enhanced serial number generation system (IELTS-YYYY-NNNNNN format)
    - Certificate authenticity validation
21. **Performance Charts**: Data visualization components:
    - Score breakdown charts with progress bars
    - Performance comparison charts with global averages
    - Score distribution visualization
    - Performance insights and recommendations
22. **Enhanced Search Integration**: Updated search page with "View Details" links
23. **Database Schema Updates**: Added certificateSerial and aiFeedbackGenerated fields

### 🚧 Remaining Features
1. **Email Notifications**: Automated notifications for results and certificates
2. **Reporting Dashboard**: Advanced analytics and reporting
3. **Bulk Operations**: Bulk candidate import, bulk result operations
4. **Mobile Optimization**: Enhanced mobile responsiveness
5. **Performance Optimization**: Caching, pagination improvements
6. **Security Enhancements**: Rate limiting, audit logging
7. **Integration Features**: External API integrations, webhooks

## Environment Variables Required
```env
# Core Application
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
DATABASE_URL=postgresql://ielts-certification-system_owner:<EMAIL>/ielts-certification-system?sslmode=require

# AI Integration
ANTHROPIC_API_KEY=your-claude-api-key

# Admin Credentials (for demo)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Public URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Configuration Files

### Next.js Configuration (`next.config.js`)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    ppr: true, // Partial Pre-rendering
    dynamicIO: true, // Dynamic IO optimization
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.vercel-storage.com',
      },
    ],
  },
  eslint: {
    dirs: ['src'],
  },
  typescript: {
    ignoreBuildErrors: false,
  },
};

module.exports = nextConfig;
```

### Drizzle Configuration (`drizzle.config.ts`)
```typescript
import type { Config } from 'drizzle-kit';

export default {
  schema: './src/lib/db/schema.ts',
  out: './src/lib/db/migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
} satisfies Config;
```

### TypeScript Configuration (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"],
      "@/stores/*": ["./src/stores/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### ESLint Configuration (`.eslintrc.json`)
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/consistent-type-imports": "error",
    "prefer-const": "error",
    "no-var": "error"
  },
  "ignorePatterns": ["node_modules/", ".next/", "out/"]
}
```

### Prettier Configuration (`.prettierrc`)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 80,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## Project Setup & Dependencies

### Package.json Template
```json
{
  "name": "ielts-certification-system",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbo",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "test": "vitest",
    "test:e2e": "playwright test",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit migrate",
    "db:studio": "drizzle-kit studio",
    "db:seed": "tsx scripts/seed.ts",
    "prepare": "husky install",
    "vercel-build": "npm run db:migrate && npm run build"
  },
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@auth/core": "^0.35.0",
    "@auth/drizzle-adapter": "^1.5.0",
    "next-auth": "5.0.0-beta.22",
    "@vercel/postgres": "^0.10.0",
    "@vercel/blob": "^0.24.0",
    "drizzle-orm": "^0.33.0",
    "drizzle-kit": "^0.25.0",
    "@anthropic-ai/sdk": "^0.27.0",
    "zod": "^3.23.0",
    "zustand": "^4.4.0",
    "tailwindcss": "^4.0.0",
    "@tailwindcss/forms": "^0.5.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-dialog": "^1.1.0",
    "@radix-ui/react-dropdown-menu": "^2.1.0",
    "@radix-ui/react-toast": "^1.2.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "tailwind-merge": "^2.5.0",
    "lucide-react": "^0.445.0",
    "puppeteer": "^23.5.0",
    "chrome-aws-lambda": "^10.1.0",
    "date-fns": "^4.1.0",
    "sharp": "^0.33.0",
    "nanoid": "^5.0.0"
  },
  "devDependencies": {
    "@types/node": "^22.7.0",
    "@types/react": "^18.3.0",
    "@types/react-dom": "^18.3.0",
    "typescript": "^5.6.0",
    "eslint": "^9.11.0",
    "eslint-config-next": "^15.0.0",
    "@typescript-eslint/eslint-plugin": "^8.8.0",
    "@typescript-eslint/parser": "^8.8.0",
    "prettier": "^3.3.0",
    "prettier-plugin-tailwindcss": "^0.6.0",
    "husky": "^9.1.0",
    "lint-staged": "^15.2.0",
    "vitest": "^2.1.0",
    "@vitejs/plugin-react": "^4.3.0",
    "playwright": "^1.47.0",
    "@playwright/test": "^1.47.0",
    "tsx": "^4.19.0"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.{json,md,css}": ["prettier --write"]
  }
}
```

### Vercel Build Settings
- **Framework Preset**: Next.js
- **Build Command**: `npm run vercel-build`
- **Output Directory**: `.next`
- **Install Command**: `npm ci`
- **Development Command**: `npm run dev`

## Performance Benchmarks (Vercel-Optimized)
- **Cold Start Time**: < 1 second
- **Page Load Time**: < 1.5 seconds (with Vercel Edge Network)
- **Database Queries**: < 200ms (including connection time)
- **Image Optimization**: Automatic WebP/AVIF conversion
- **PDF Generation**: < 5 seconds (serverless function timeout)
- **AI Feedback**: < 15 seconds (with extended timeout)
- **Global CDN**: Sub-100ms static asset delivery

## Test Checker Workflow & Results Entry System

### Overview
The IELTS Certification System provides a comprehensive workflow for test checkers to enter, manage, and verify IELTS test results. The system follows official IELTS scoring standards and automates band score calculations while maintaining accuracy and consistency.

### Test Checker Dashboard (`/dashboard`)

#### Dashboard Features
- **Personal Statistics**: Shows total results entered, pending results, completed results, and certificates generated by the logged-in checker
- **Quick Actions**: Direct access to enter new results, search candidates, and view recent results
- **Performance Metrics**: Visual charts showing checker productivity and result status distribution
- **Recent Activity**: List of recently entered or modified results

#### Navigation Structure
```
/dashboard
├── /                    # Main dashboard with stats
├── /search             # Search candidates for result entry
├── /results            # Enter new test results
├── /results/list       # View all results entered by checker
├── /results/[id]       # View individual result details
└── /results/[id]/edit  # Edit existing results
```

### Results Entry Process

#### Step 1: Candidate Selection (`/dashboard/search`)
Test checkers begin by searching for candidates using multiple criteria:
- **Full Name**: Search by candidate's complete name
- **Passport Number**: Unique identifier search
- **Email Address**: Contact-based search
- **Test Date**: Filter by specific test dates
- **Test Center**: Filter by examination center

#### Step 2: Results Entry Form (`/dashboard/results`)
Once a candidate is selected, checkers access a comprehensive results entry form with the following sections:

##### Listening Section
- **Raw Score**: Number of correct answers (0-40)
- **Band Score**: Automatically calculated or manually entered (0-9, increments of 0.5)
- **Conversion Table**: Built-in conversion from raw scores to band scores

##### Reading Section
- **Raw Score**: Number of correct answers (0-40)
- **Band Score**: Automatically calculated or manually entered (0-9, increments of 0.5)
- **Conversion Table**: Identical to listening for Academic IELTS

##### Writing Section
- **Task 1 Score**: Band score for Task 1 (0-9, increments of 0.5)
- **Task 2 Score**: Band score for Task 2 (0-9, increments of 0.5)
- **Overall Writing Band**: Calculated as weighted average (Task 2 has more weight)

##### Speaking Section
- **Fluency and Coherence**: Band score (0-9, increments of 0.5)
- **Lexical Resource**: Vocabulary usage score (0-9, increments of 0.5)
- **Grammatical Range and Accuracy**: Grammar score (0-9, increments of 0.5)
- **Pronunciation**: Pronunciation clarity score (0-9, increments of 0.5)
- **Overall Speaking Band**: Average of four criteria

##### Overall Band Score
- **Automatic Calculation**: System calculates the average of four skills
- **Rounding Rules**: Rounded to nearest 0.5 (e.g., 6.75 becomes 7.0, 6.25 becomes 6.5)
- **Manual Override**: Checkers can manually adjust if needed

### IELTS Band Score Calculation System

#### Raw Score to Band Score Conversion (Listening & Reading)

The system implements official IELTS conversion tables:

```typescript
// Listening/Reading Conversion Table (out of 40 questions)
const conversionTable = {
  39-40: 9.0,  // Expert User
  37-38: 8.5,  // Very Good User
  35-36: 8.0,  // Very Good User
  33-34: 7.5,  // Good User
  31-32: 7.0,  // Good User
  29-30: 6.5,  // Competent User
  27-28: 6.0,  // Competent User
  25-26: 5.5,  // Modest User
  23-24: 5.0,  // Modest User
  21-22: 4.5,  // Limited User
  19-20: 4.0,  // Limited User
  17-18: 3.5,  // Extremely Limited User
  15-16: 3.0,  // Extremely Limited User
  13-14: 2.5,  // Intermittent User
  11-12: 2.0,  // Intermittent User
  9-10:  1.5,  // Non User
  7-8:   1.0,  // Non User
  0-6:   0.0   // Did not attempt
};
```

#### Writing Assessment Criteria
Writing tasks are assessed on four criteria, each scored 0-9:
1. **Task Achievement/Response**: How well the task requirements are met
2. **Coherence and Cohesion**: Organization and linking of ideas
3. **Lexical Resource**: Vocabulary range and accuracy
4. **Grammatical Range and Accuracy**: Grammar variety and correctness

**Writing Band Calculation**:
- Task 1 weight: 33%
- Task 2 weight: 67%
- Formula: `(Task1 × 1 + Task2 × 2) ÷ 3`

#### Speaking Assessment Criteria
Speaking is assessed on four equally weighted criteria:
1. **Fluency and Coherence**: Speech flow and organization
2. **Lexical Resource**: Vocabulary usage and appropriateness
3. **Grammatical Range and Accuracy**: Grammar complexity and correctness
4. **Pronunciation**: Clarity and natural speech patterns

**Speaking Band Calculation**:
- Equal weight for all four criteria
- Formula: `(Fluency + Lexical + Grammar + Pronunciation) ÷ 4`

#### Overall Band Score Calculation
```typescript
function calculateOverallBandScore(
  listening: number,
  reading: number,
  writing: number,
  speaking: number
): number {
  const average = (listening + reading + writing + speaking) / 4;
  return Math.round(average * 2) / 2; // Round to nearest 0.5
}
```

### Result Status Management

#### Status Workflow
1. **Pending**: Initial status when results are first entered
2. **Completed**: Results reviewed and confirmed by checker
3. **Verified**: Results verified by senior examiner or admin

#### Status Transitions
- Checkers can move results from "Pending" to "Completed"
- Only admins can mark results as "Verified"
- Verified results cannot be edited without admin permission

### Quality Assurance Features

#### Validation Rules
- **Score Ranges**: All scores must be within valid IELTS ranges
- **Increment Validation**: Band scores must be in 0.5 increments
- **Consistency Checks**: System flags unusual score patterns
- **Required Fields**: Essential scores must be completed before submission

#### Error Prevention
- **Real-time Calculation**: Overall band score updates automatically
- **Visual Feedback**: Color-coded indicators for score ranges
- **Confirmation Dialogs**: Warnings for unusual score combinations
- **Save Draft**: Ability to save incomplete results and return later

### Results Management Interface

#### Results List View (`/dashboard/results/list`)
- **Filterable Table**: Filter by status, date range, candidate name
- **Sortable Columns**: Sort by any column (date, score, status)
- **Bulk Actions**: Select multiple results for batch operations
- **Export Options**: Export filtered results to CSV
- **Quick Actions**: View, edit, or generate certificates directly from list

#### Individual Result View (`/dashboard/results/[id]`)
- **Complete Score Breakdown**: All scores displayed with visual charts
- **Candidate Information**: Full candidate details and photo
- **Action Buttons**: Edit results, generate certificate, create AI feedback
- **Audit Trail**: History of changes and who made them
- **Status Management**: Change result status with confirmation

#### Results Editing (`/dashboard/results/[id]/edit`)
- **Pre-populated Form**: All existing scores loaded for editing
- **Change Tracking**: System tracks what fields were modified
- **Validation**: Same validation rules as initial entry
- **Confirmation**: Requires confirmation before saving changes

### API Endpoints for Test Checkers

#### Dashboard & Statistics
- `GET /api/checker/dashboard` - Get checker-specific statistics
- `GET /api/checker/candidates/search` - Search candidates for result entry

#### Results Management
- `POST /api/checker/results` - Create new test result
- `GET /api/checker/results` - List results entered by checker (paginated)
- `GET /api/checker/results/[id]` - Get specific result details
- `PUT /api/checker/results/[id]` - Update existing result
- `DELETE /api/checker/results/[id]` - Delete result (admin only)

#### Certificate Generation
- `POST /api/certificate/[id]` - Generate PDF certificate
- `GET /api/certificate/[id]` - Download generated certificate

### Data Validation & Security

#### Input Validation
```typescript
const testResultSchema = z.object({
  candidateId: z.string().min(1),
  listeningScore: z.number().min(0).max(40).optional(),
  listeningBandScore: z.number().min(0).max(9).step(0.5).optional(),
  readingScore: z.number().min(0).max(40).optional(),
  readingBandScore: z.number().min(0).max(9).step(0.5).optional(),
  writingTask1Score: z.number().min(0).max(9).step(0.5).optional(),
  writingTask2Score: z.number().min(0).max(9).step(0.5).optional(),
  writingBandScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingFluencyScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingBandScore: z.number().min(0).max(9).step(0.5).optional(),
  overallBandScore: z.number().min(0).max(9).step(0.5).optional(),
});
```

#### Security Measures
- **Authentication Required**: All checker endpoints require valid session
- **Role-based Access**: Only test_checker and admin roles can enter results
- **Audit Logging**: All result entries and modifications are logged
- **Data Integrity**: Database constraints prevent invalid data entry

### Integration with AI Feedback System

#### Automatic Feedback Generation
- **Trigger**: AI feedback can be generated after result completion
- **Input Data**: Uses all score components for comprehensive analysis
- **Personalization**: Considers candidate nationality and score patterns
- **Storage**: Feedback stored in database and linked to result

#### Feedback Components
- **Module-specific Feedback**: Tailored advice for each IELTS skill
- **Overall Assessment**: Comprehensive performance evaluation
- **Study Recommendations**: Personalized improvement suggestions
- **Strengths & Weaknesses**: Identified based on score analysis

### Certificate Generation Workflow

#### Automatic Certificate Creation
1. **Trigger**: When result status changes to "Completed" or "Verified"
2. **Serial Generation**: Unique serial number (IELTS-YYYY-NNNNNN format)
3. **PDF Creation**: Professional certificate with all score details
4. **Database Update**: Certificate status and serial number saved

#### Certificate Features
- **Official Branding**: IELTS logo and professional layout
- **Complete Score Table**: All module scores and descriptions
- **Security Features**: Unique serial number and verification QR code
- **Download Options**: Available to checkers and candidates

### Performance Monitoring

#### System Metrics
- **Entry Speed**: Average time to complete result entry
- **Error Rates**: Validation errors and correction frequency
- **Checker Productivity**: Results entered per checker per day
- **Quality Scores**: Accuracy of entered results vs. verification

#### Reporting Dashboard
- **Daily Statistics**: Results entered, certificates generated
- **Checker Performance**: Individual and team productivity metrics
- **Error Analysis**: Common mistakes and training needs
- **System Health**: API response times and error rates

## Admin Workflow & System Management

### Admin Dashboard (`/admin`)

#### Administrative Features
- **System Overview**: Total candidates, results, certificates, and active users
- **User Management**: Create, edit, and manage test checker accounts
- **Data Analytics**: Comprehensive reporting and performance metrics
- **System Configuration**: Manage test centers, scoring parameters, and system settings
- **Audit Logs**: Track all system activities and changes

#### Navigation Structure
```
/admin
├── /                    # Main admin dashboard
├── /candidates          # Candidate management
├── /candidates/add      # Add new candidates
├── /candidates/[id]     # Individual candidate details
├── /users              # User management (test checkers)
├── /results            # All results overview
├── /certificates       # Certificate management
├── /analytics          # Advanced analytics
├── /settings           # System configuration
└── /audit              # Audit logs and security
```

### Candidate Management System

#### Candidate Registration (`/admin/candidates/add`)
Admins can register new candidates with comprehensive information:

##### Personal Information
- **Full Name**: Complete legal name as per passport
- **Date of Birth**: For age verification and record keeping
- **Nationality**: Country of citizenship
- **Gender**: For demographic tracking
- **Contact Information**: Email, phone number, address

##### Test Details
- **Test Type**: Academic or General Training IELTS
- **Test Date**: Scheduled examination date
- **Test Center**: Examination venue
- **Test Session**: Morning/Afternoon session
- **Special Requirements**: Accessibility needs or accommodations

##### Documentation
- **Passport Number**: Primary identification
- **Passport Expiry**: Validity verification
- **Photo Upload**: Candidate photograph for identification
- **Additional Documents**: Any supporting documentation

#### Candidate Search & Management (`/admin/candidates`)
- **Advanced Search**: Multi-criteria search across all candidate fields
- **Bulk Operations**: Import candidates from CSV, bulk updates
- **Status Tracking**: Monitor test completion and result status
- **Communication**: Send notifications and updates to candidates
- **Data Export**: Export candidate lists and statistics

### Results Verification & Quality Control

#### Result Review Process
1. **Initial Entry**: Test checkers enter results (status: "Pending")
2. **Checker Review**: Checkers verify their own entries (status: "Completed")
3. **Admin Verification**: Admins perform final verification (status: "Verified")
4. **Certificate Generation**: Automatic certificate creation upon verification

#### Quality Assurance Tools
- **Score Distribution Analysis**: Identify unusual scoring patterns
- **Checker Performance Metrics**: Monitor accuracy and consistency
- **Automated Flags**: System alerts for scores requiring review
- **Comparative Analysis**: Compare scores across test centers and dates

### User Management System

#### Test Checker Account Management
- **Account Creation**: Create new test checker accounts
- **Role Assignment**: Assign specific permissions and access levels
- **Performance Tracking**: Monitor checker productivity and accuracy
- **Training Records**: Track certification and training completion
- **Access Control**: Manage system access and restrictions

#### User Roles & Permissions
```typescript
interface UserRole {
  admin: {
    candidates: ['create', 'read', 'update', 'delete'],
    results: ['create', 'read', 'update', 'delete', 'verify'],
    users: ['create', 'read', 'update', 'delete'],
    certificates: ['generate', 'revoke', 'verify'],
    system: ['configure', 'audit', 'backup']
  },
  test_checker: {
    candidates: ['read', 'search'],
    results: ['create', 'read', 'update'],
    certificates: ['generate', 'download'],
    system: ['dashboard']
  }
}
```

### Certificate Management & Security

#### Certificate Lifecycle
1. **Generation**: Automatic creation upon result verification
2. **Serial Assignment**: Unique IELTS-YYYY-NNNNNN serial number
3. **Digital Signing**: Cryptographic signature for authenticity
4. **Storage**: Secure storage with backup and redundancy
5. **Distribution**: Download access for candidates and institutions

#### Security Features
- **Unique Serial Numbers**: Tamper-proof identification system
- **QR Code Verification**: Quick verification via mobile scanning
- **Digital Watermarks**: Embedded security features in PDF
- **Audit Trail**: Complete history of certificate generation and access
- **Revocation System**: Ability to revoke certificates if needed

### Advanced Analytics & Reporting

#### Performance Analytics
- **Score Distributions**: Statistical analysis of test results
- **Trend Analysis**: Performance trends over time
- **Demographic Insights**: Results by nationality, age, test center
- **Comparative Studies**: Performance across different test sessions

#### Operational Reports
- **Daily Operations**: Results entered, certificates generated
- **Checker Performance**: Individual and team productivity
- **System Usage**: API calls, page views, user activity
- **Error Reports**: System errors, validation failures, user issues

#### Export & Integration
- **Data Export**: CSV, Excel, PDF report generation
- **API Integration**: External system integration capabilities
- **Automated Reports**: Scheduled report generation and distribution
- **Dashboard Widgets**: Customizable admin dashboard components

### System Configuration & Settings

#### Test Center Management
- **Center Registration**: Add new test centers and locations
- **Capacity Management**: Set maximum candidates per session
- **Staff Assignment**: Assign test checkers to specific centers
- **Equipment Tracking**: Monitor test equipment and resources

#### Scoring Configuration
- **Band Score Tables**: Manage conversion tables for different test versions
- **Validation Rules**: Configure score validation and business rules
- **Calculation Parameters**: Adjust overall band score calculation methods
- **Quality Thresholds**: Set thresholds for automatic quality checks

#### System Parameters
- **Email Templates**: Configure notification and communication templates
- **File Upload Limits**: Set maximum file sizes and allowed formats
- **Session Timeouts**: Configure user session and security parameters
- **Backup Schedules**: Manage automated backup and recovery procedures

### Data Security & Compliance

#### Data Protection
- **Encryption**: All sensitive data encrypted at rest and in transit
- **Access Logging**: Complete audit trail of data access and modifications
- **Data Retention**: Configurable retention policies for different data types
- **Privacy Controls**: GDPR compliance and data subject rights management

#### Backup & Recovery
- **Automated Backups**: Daily automated database and file backups
- **Point-in-time Recovery**: Ability to restore to specific timestamps
- **Disaster Recovery**: Comprehensive disaster recovery procedures
- **Data Integrity**: Regular integrity checks and validation

#### Compliance Features
- **Audit Logs**: Comprehensive logging of all system activities
- **Data Export**: Ability to export candidate data upon request
- **Data Deletion**: Secure deletion of candidate data when required
- **Compliance Reports**: Generate reports for regulatory compliance

### Integration Capabilities

#### External System Integration
- **Student Information Systems**: Integration with educational institutions
- **Immigration Systems**: Direct result sharing with immigration authorities
- **University Portals**: Automatic result submission to universities
- **Third-party Verification**: API for external verification services

#### API Documentation
```typescript
// Public API Endpoints
GET /api/results/[id]              // Public result access
GET /api/feedback/[resultId]       // Public feedback access
GET /api/certificate/verify/[serial] // Certificate verification

// Admin API Endpoints
GET /api/admin/dashboard           // Admin statistics
GET /api/admin/candidates          // Candidate management
POST /api/admin/candidates         // Create candidate
GET /api/admin/users              // User management
POST /api/admin/users             // Create user

// Checker API Endpoints
GET /api/checker/dashboard         // Checker statistics
POST /api/checker/results          // Create result
GET /api/checker/results           // List results
PUT /api/checker/results/[id]      // Update result
```

### Deployment & Infrastructure

#### Production Environment
- **Vercel Deployment**: Optimized for Next.js applications
- **PostgreSQL Database**: Managed database with automatic backups
- **CDN Integration**: Global content delivery for optimal performance
- **SSL/TLS Security**: End-to-end encryption for all communications

#### Monitoring & Alerting
- **Application Monitoring**: Real-time performance and error tracking
- **Database Monitoring**: Query performance and connection monitoring
- **User Activity Tracking**: Monitor user behavior and system usage
- **Automated Alerts**: Notifications for system issues and anomalies

#### Scalability Features
- **Horizontal Scaling**: Auto-scaling based on traffic and load
- **Database Optimization**: Query optimization and indexing strategies
- **Caching Layers**: Redis caching for improved performance
- **Load Balancing**: Distributed load handling for high availability

### Training & Support

#### User Training Materials
- **Admin Guide**: Comprehensive administration manual
- **Checker Training**: Test checker certification program
- **Video Tutorials**: Step-by-step video guides for all features
- **Best Practices**: Guidelines for optimal system usage

#### Support Infrastructure
- **Help Desk**: Integrated support ticket system
- **Knowledge Base**: Searchable documentation and FAQs
- **User Forums**: Community support and discussion
- **Technical Support**: Direct access to technical assistance

### Future Enhancements

#### Planned Features
- **Mobile Application**: Native mobile app for test checkers
- **Blockchain Verification**: Immutable certificate verification
- **AI-Powered Analytics**: Advanced predictive analytics
- **Multi-language Support**: Internationalization for global use

#### Integration Roadmap
- **LMS Integration**: Learning Management System connectivity
- **Biometric Verification**: Enhanced security with biometric data
- **Real-time Collaboration**: Multi-checker result verification
- **Advanced Reporting**: Business intelligence and data visualization