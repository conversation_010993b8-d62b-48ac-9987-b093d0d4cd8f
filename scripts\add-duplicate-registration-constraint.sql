-- Add constraint to prevent duplicate registrations for the same candidate on the same test date
-- This ensures data integrity at the database level

BEGIN;

-- Add unique constraint to prevent duplicate registrations
-- This constraint ensures that a candidate can only register once per test date
ALTER TABLE test_registrations 
ADD CONSTRAINT test_registrations_candidate_id_test_date_unique 
UNIQUE (candidate_id, test_date);

-- Add unique constraint for candidate number per test date
-- This ensures that candidate numbers are unique within each test date
ALTER TABLE test_registrations 
ADD CONSTRAINT test_registrations_candidate_number_test_date_unique 
UNIQUE (candidate_number, test_date);

COMMIT;

-- Verify the constraints were added
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'test_registrations'::regclass 
AND contype = 'u'
ORDER BY conname;
