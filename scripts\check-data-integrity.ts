import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

async function checkDataIntegrity() {
  console.log('🔍 Checking IELTS Certification System Data Integrity...\n');

  try {
    // Import database and schema after environment is loaded
    const { db } = await import('../src/lib/db');
    const { candidates, testRegistrations, testResults } = await import('../src/lib/db/schema');
    const { eq, isNull, count, sql } = await import('drizzle-orm');
    // Check 1: Orphaned test results (results without valid test registrations)
    console.log('1️⃣ Checking for orphaned test results...');
    const orphanedResults = await db
      .select({
        id: testResults.id,
        testRegistrationId: testResults.testRegistrationId,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .where(isNull(testRegistrations.id));

    if (orphanedResults.length > 0) {
      console.log(`❌ Found ${orphanedResults.length} orphaned test results:`);
      orphanedResults.forEach(result => {
        console.log(`   - Result ID: ${result.id}, Missing Registration ID: ${result.testRegistrationId}`);
      });
    } else {
      console.log('✅ No orphaned test results found');
    }

    // Check 2: Orphaned test registrations (registrations without valid candidates)
    console.log('\n2️⃣ Checking for orphaned test registrations...');
    const orphanedRegistrations = await db
      .select({
        id: testRegistrations.id,
        candidateId: testRegistrations.candidateId,
        candidateNumber: testRegistrations.candidateNumber,
      })
      .from(testRegistrations)
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(isNull(candidates.id));

    if (orphanedRegistrations.length > 0) {
      console.log(`❌ Found ${orphanedRegistrations.length} orphaned test registrations:`);
      orphanedRegistrations.forEach(reg => {
        console.log(`   - Registration ID: ${reg.id}, Missing Candidate ID: ${reg.candidateId}, Candidate Number: ${reg.candidateNumber}`);
      });
    } else {
      console.log('✅ No orphaned test registrations found');
    }

    // Check 3: Duplicate candidate registrations for same test date
    console.log('\n3️⃣ Checking for duplicate registrations...');
    const duplicateRegistrations = await db
      .select({
        candidateId: testRegistrations.candidateId,
        testDate: testRegistrations.testDate,
        count: count(),
      })
      .from(testRegistrations)
      .groupBy(testRegistrations.candidateId, testRegistrations.testDate)
      .having(sql`count(*) > 1`);

    if (duplicateRegistrations.length > 0) {
      console.log(`❌ Found ${duplicateRegistrations.length} duplicate registrations:`);
      duplicateRegistrations.forEach(dup => {
        console.log(`   - Candidate ID: ${dup.candidateId}, Test Date: ${dup.testDate}, Count: ${dup.count}`);
      });
    } else {
      console.log('✅ No duplicate registrations found');
    }

    // Check 4: Test results without corresponding candidates
    console.log('\n4️⃣ Checking test results to candidates relationship...');
    const resultsWithoutCandidates = await db
      .select({
        resultId: testResults.id,
        registrationId: testRegistrations.id,
        candidateId: candidates.id,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(isNull(candidates.id));

    if (resultsWithoutCandidates.length > 0) {
      console.log(`❌ Found ${resultsWithoutCandidates.length} test results without valid candidates`);
    } else {
      console.log('✅ All test results have valid candidate relationships');
    }

    // Check 5: Summary statistics
    console.log('\n5️⃣ Database summary statistics:');

    const candidatesCount = await db.select({ count: count() }).from(candidates);
    const registrationsCount = await db.select({ count: count() }).from(testRegistrations);
    const resultsCount = await db.select({ count: count() }).from(testResults);

    console.log(`   📊 Total candidates: ${candidatesCount[0]?.count || 0}`);
    console.log(`   📊 Total test registrations: ${registrationsCount[0]?.count || 0}`);
    console.log(`   📊 Total test results: ${resultsCount[0]?.count || 0}`);

    // Check 6: Recent data samples
    console.log('\n6️⃣ Recent data samples:');

    const recentCandidates = await db
      .select({
        id: candidates.id,
        fullName: candidates.fullName,
        passportNumber: candidates.passportNumber,
        createdAt: candidates.createdAt,
      })
      .from(candidates)
      .orderBy(candidates.createdAt)
      .limit(3);

    console.log('   Recent candidates:');
    recentCandidates.forEach(candidate => {
      console.log(`   - ${candidate.fullName} (${candidate.passportNumber}) - ${candidate.createdAt}`);
    });

    const recentRegistrations = await db
      .select({
        id: testRegistrations.id,
        candidateNumber: testRegistrations.candidateNumber,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
      })
      .from(testRegistrations)
      .orderBy(testRegistrations.createdAt)
      .limit(3);

    console.log('\n   Recent registrations:');
    recentRegistrations.forEach(reg => {
      console.log(`   - ${reg.candidateNumber} on ${reg.testDate} at ${reg.testCenter}`);
    });

    // Check 7: Test specific data relationships
    console.log('\n7️⃣ Testing data relationships...');

    const sampleResult = await db
      .select({
        resultId: testResults.id,
        candidateName: candidates.fullName,
        candidatePassport: candidates.passportNumber,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        overallScore: testResults.overallBandScore,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .limit(1);

    if (sampleResult.length > 0) {
      const sample = sampleResult[0];
      console.log('✅ Sample relationship working:');
      console.log(`   - Result ID: ${sample.resultId}`);
      console.log(`   - Candidate: ${sample.candidateName} (${sample.candidatePassport})`);
      console.log(`   - Test: ${sample.testDate} at ${sample.testCenter}`);
      console.log(`   - Score: ${sample.overallScore || 'N/A'}`);
    } else {
      console.log('❌ No complete relationships found');
    }

    console.log('\n🎉 Data integrity check completed!');

  } catch (error) {
    console.error('❌ Error during data integrity check:', error);
  }
}

// Run the check
checkDataIntegrity().catch(console.error);
