-- Migration script to add missing fields to the IELTS certification system
-- Run this script to update the database schema

-- Add missing fields to test_results table
ALTER TABLE test_results 
ADD COLUMN IF NOT EXISTS certificate_serial TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS ai_feedback_generated BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS test_date TIMESTAMP;

-- Update ai_feedback table to match API expectations
ALTER TABLE ai_feedback 
ADD COLUMN IF NOT EXISTS study_recommendations TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_test_results_certificate_serial ON test_results(certificate_serial);
CREATE INDEX IF NOT EXISTS idx_test_results_ai_feedback ON test_results(ai_feedback_generated);
CREATE INDEX IF NOT EXISTS idx_test_results_status ON test_results(status);
CREATE INDEX IF NOT EXISTS idx_candidates_passport ON candidates(passport_number);
CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email);

-- Update existing records to set ai_feedback_generated based on existing feedback
UPDATE test_results 
SET ai_feedback_generated = TRUE 
WHERE id IN (
    SELECT DISTINCT test_result_id 
    FROM ai_feedback
);

-- Add comments for documentation
COMMENT ON COLUMN test_results.certificate_serial IS 'Unique certificate serial number in format IELTS-YYYY-NNNNNN';
COMMENT ON COLUMN test_results.ai_feedback_generated IS 'Flag indicating if AI feedback has been generated for this result';
COMMENT ON COLUMN ai_feedback.study_recommendations IS 'AI-generated study recommendations for the candidate';
