/**
 * Test script for IELTS API endpoints
 * Tests the critical functionality: AI feedback generation, certificate generation, and public access
 */

const BASE_URL = 'http://localhost:3001';

async function testEndpoint(url, options = {}) {
  try {
    console.log(`\n🧪 Testing: ${options.method || 'GET'} ${url}`);
    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(data, null, 2));
    
    return { success: response.ok, status: response.status, data };
  } catch (error) {
    console.log(`   ❌ Error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting IELTS API Endpoint Tests\n');
  
  // Test 1: Check if server is running
  console.log('='.repeat(50));
  console.log('TEST 1: Server Health Check');
  console.log('='.repeat(50));
  
  const healthCheck = await testEndpoint(`${BASE_URL}/api/health`);
  if (!healthCheck.success) {
    console.log('❌ Server is not responding. Make sure the development server is running.');
    return;
  }
  
  // Test 2: Test public search endpoint
  console.log('\n' + '='.repeat(50));
  console.log('TEST 2: Public Search Endpoint');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: 'test',
      searchType: 'name'
    })
  });
  
  // Test 3: Test public result access (this will likely fail without a real result ID)
  console.log('\n' + '='.repeat(50));
  console.log('TEST 3: Public Result Access');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/results/test-id`);
  
  // Test 4: Test public feedback access
  console.log('\n' + '='.repeat(50));
  console.log('TEST 4: Public Feedback Access');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/feedback/test-id`);
  
  // Test 5: Test certificate verification
  console.log('\n' + '='.repeat(50));
  console.log('TEST 5: Certificate Verification');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/certificate/verify/IELTS-2024-123456`);
  
  // Test 6: Test AI feedback generation endpoint
  console.log('\n' + '='.repeat(50));
  console.log('TEST 6: AI Feedback Generation');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/results/test-id/generate-feedback`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  });
  
  // Test 7: Test certificate generation endpoint
  console.log('\n' + '='.repeat(50));
  console.log('TEST 7: Certificate Generation');
  console.log('='.repeat(50));
  
  await testEndpoint(`${BASE_URL}/api/results/test-id/generate-certificate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  });
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ API Endpoint Tests Completed');
  console.log('='.repeat(50));
  console.log('\nNote: Some tests may fail due to missing test data, but they should show proper error handling.');
  console.log('The important thing is that endpoints are accessible and return proper HTTP status codes.');
}

// Run the tests
runTests().catch(console.error);
