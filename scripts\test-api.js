/**
 * Test script for the new public API endpoints
 * Run with: node scripts/test-api.js
 */

const BASE_URL = 'http://localhost:3000';

async function testAPI(endpoint, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📍 Endpoint: ${endpoint}`);
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`);
    const data = await response.json();
    
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`📄 Response:`, JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Test passed');
    } else {
      console.log('❌ Test failed');
    }
  } catch (error) {
    console.log('💥 Error:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests for IELTS Certification System');
  console.log('=' .repeat(60));

  // Test public results API with a non-existent ID (should return 404)
  await testAPI('/api/results/999', 'Public Results API - Non-existent ID');

  // Test public results API with invalid ID format
  await testAPI('/api/results/invalid', 'Public Results API - Invalid ID format');

  // Test feedback API with non-existent result ID
  await testAPI('/api/feedback/999', 'Public Feedback API - Non-existent result');

  // Test certificate verification with invalid serial
  await testAPI('/api/certificate/verify/INVALID-SERIAL', 'Certificate Verification - Invalid serial');

  // Test certificate verification with proper format but non-existent serial
  await testAPI('/api/certificate/verify/IELTS-2024-999999', 'Certificate Verification - Non-existent certificate');

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 API Tests completed');
  console.log('\n📝 Note: These tests use non-existent IDs to verify error handling.');
  console.log('   To test with real data, you need to:');
  console.log('   1. Create test candidates and results through the admin panel');
  console.log('   2. Generate certificates for the results');
  console.log('   3. Use the actual IDs and serial numbers in the tests');
}

// Run the tests
runTests().catch(console.error);
