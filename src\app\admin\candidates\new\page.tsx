'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, Save, Upload, User, Mail, Calendar, Users, CheckCircle } from 'lucide-react';
import FileUpload from '@/components/FileUpload';

interface ExistingCandidate {
  id: string;
  candidateNumber: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  testDate: string;
  testCenter: string;
  photoUrl?: string;
  photoData?: string;
}

export default function NewCandidatePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchingCandidates, setSearchingCandidates] = useState(false);
  const [existingCandidates, setExistingCandidates] = useState<ExistingCandidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<ExistingCandidate | null>(null);
  const [showCandidateSelection, setShowCandidateSelection] = useState(false);
  const [formData, setFormData] = useState({
    candidateNumber: '',
    fullName: '',
    email: '',
    phoneNumber: '',
    dateOfBirth: '',
    nationality: '',
    identificationType: 'passport', // For user clarity only
    passportNumber: '', // Stores both passport and birth certificate numbers
    testDate: '',
    testCenter: '',
    photoUrl: '',
    photoData: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Search for existing candidates when passport/birth certificate number or email is entered
    if (name === 'passportNumber' && value.trim().length >= 3) {
      searchExistingCandidates(value.trim());
    } else if (name === 'passportNumber' && value.trim().length < 3) {
      setExistingCandidates([]);
      setShowCandidateSelection(false);
    }

    // Also search by email address
    if (name === 'email' && value.trim().includes('@') && value.trim().length >= 5) {
      searchExistingCandidates(value.trim());
    } else if (name === 'email' && (!value.trim().includes('@') || value.trim().length < 5)) {
      // Only clear if we're not showing results from passport search
      if (formData.passportNumber.trim().length < 3) {
        setExistingCandidates([]);
        setShowCandidateSelection(false);
      }
    }
  };

  const searchExistingCandidates = async (searchQuery: string) => {
    if (!searchQuery) return;

    setSearchingCandidates(true);
    try {
      const response = await fetch('/api/admin/candidates/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery, // Can be email or passport/birth certificate number
        }),
      });

      if (response.ok) {
        const candidates = await response.json();
        setExistingCandidates(candidates);
        setShowCandidateSelection(candidates.length > 0);
      } else {
        setExistingCandidates([]);
        setShowCandidateSelection(false);
      }
    } catch (error) {
      console.error('Error searching candidates:', error);
      setExistingCandidates([]);
      setShowCandidateSelection(false);
    } finally {
      setSearchingCandidates(false);
    }
  };

  const selectExistingCandidate = (candidate: ExistingCandidate) => {
    setSelectedCandidate(candidate);
    setFormData({
      candidateNumber: '', // Will be auto-generated for new test registration
      fullName: candidate.fullName,
      email: candidate.email,
      phoneNumber: candidate.phoneNumber,
      dateOfBirth: candidate.dateOfBirth.split('T')[0], // Format date for input
      nationality: candidate.nationality,
      identificationType: 'passport', // Default to passport
      passportNumber: candidate.passportNumber,
      testDate: '', // New test date to be selected
      testCenter: 'Innovative Centre - Samarkand', // Default test center
      photoUrl: candidate.photoUrl || '',
      photoData: candidate.photoData || '',
    });
    setShowCandidateSelection(false);
  };

  const createNewCandidate = () => {
    setSelectedCandidate(null);
    setShowCandidateSelection(false);
    // Keep the current form data as is for new candidate creation
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/admin/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        if (selectedCandidate) {
          // Existing candidate registered for new test
          router.push(`/admin/candidates/${result.id}?message=registered`);
        } else {
          // New candidate created
          router.push('/admin/candidates?message=created');
        }
      } else {
        const errorData = await response.json();
        if (selectedCandidate) {
          setError(errorData.error || 'Failed to register candidate for test');
        } else {
          setError(errorData.error || 'Failed to create candidate');
        }
      }
    } catch {
      if (selectedCandidate) {
        setError('An error occurred while registering for the test. Please try again.');
      } else {
        setError('An error occurred while creating the candidate. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const testCenters = [
    'Innovative Centre - Samarkand',
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/candidates"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Candidate</h1>
            <p className="text-gray-600">Register a new test candidate</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <User className="h-5 w-5 mr-2" />
              Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="candidateNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Candidate Number
                </label>
                <input
                  type="text"
                  id="candidateNumber"
                  name="candidateNumber"
                  value={formData.candidateNumber}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., 001 (auto-generated if empty)"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Leave empty to auto-generate next available number for this test date
                </p>
              </div>

              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter full name as on passport"
                />
              </div>

              <div>
                <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-2">
                  Date of Birth *
                </label>
                <input
                  type="date"
                  id="dateOfBirth"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="nationality" className="block text-sm font-medium text-gray-700 mb-2">
                  Nationality *
                </label>
                <input
                  type="text"
                  id="nationality"
                  name="nationality"
                  value={formData.nationality}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., British, American, etc."
                />
              </div>

              <div>
                <label htmlFor="identificationType" className="block text-sm font-medium text-gray-700 mb-2">
                  Identification Type *
                </label>
                <select
                  id="identificationType"
                  name="identificationType"
                  value={formData.identificationType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="passport">Passport</option>
                  <option value="birth_certificate">Birth Certificate</option>
                </select>
              </div>

              <div>
                <label htmlFor="passportNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.identificationType === 'passport' ? 'Passport Number' : 'Birth Certificate Number'} *
                </label>
                <input
                  type="text"
                  id="passportNumber"
                  name="passportNumber"
                  value={formData.passportNumber}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={`Enter ${formData.identificationType === 'passport' ? 'passport number' : 'birth certificate number'}`}
                />
                <p className="mt-1 text-xs text-gray-500">
                  This field stores both passport and birth certificate numbers for search purposes
                </p>

                {/* Search indicator */}
                {searchingCandidates && (
                  <div className="mt-2 flex items-center text-sm text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Searching for existing candidates...
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Existing Candidate Selection */}
          {showCandidateSelection && existingCandidates.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium text-blue-900">
                  Existing Candidates Found
                </h3>
              </div>
              <p className="text-blue-700 mb-4">
                We found {existingCandidates.length} existing candidate{existingCandidates.length !== 1 ? 's' : ''} matching your search.
                You can select an existing candidate to register them for a new test, or continue creating a new candidate.
              </p>

              <div className="space-y-3 mb-4">
                {existingCandidates.map((candidate) => (
                  <div
                    key={candidate.id}
                    className="bg-white border border-blue-200 rounded-lg p-4 hover:border-blue-300 transition-colors cursor-pointer"
                    onClick={() => selectExistingCandidate(candidate)}
                  >
                    <div className="flex items-center space-x-4">
                      {(candidate.photoUrl || candidate.photoData) ? (
                        <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                          <Image
                            src={candidate.photoData ? `data:image/jpeg;base64,${candidate.photoData}` : candidate.photoUrl!}
                            alt={candidate.fullName}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                      )}

                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900">{candidate.fullName}</h4>
                          <span className="text-sm text-gray-500">#{candidate.candidateNumber}</span>
                        </div>
                        <p className="text-sm text-gray-600">{candidate.email}</p>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <Calendar className="h-3 w-3 mr-1" />
                          Last test: {new Date(candidate.testDate).toLocaleDateString()}
                        </div>
                      </div>

                      <CheckCircle className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={createNewCandidate}
                  className="px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50"
                >
                  Create New Candidate Instead
                </button>
              </div>
            </div>
          )}

          {/* Selected Candidate Indicator */}
          {selectedCandidate && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-green-800 font-medium">
                  Registering existing candidate: {selectedCandidate.fullName}
                </span>
              </div>
              <p className="text-green-700 text-sm mt-1">
                Personal information has been pre-filled. Please select a new test date and verify the details.
              </p>
            </div>
          )}

          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Contact Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />

                {/* Search indicator for email */}
                {searchingCandidates && formData.email.includes('@') && formData.email.length >= 5 && (
                  <div className="mt-2 flex items-center text-sm text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Searching for existing candidates...
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+44 ************"
                />
              </div>
            </div>
          </div>

          {/* Test Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Test Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="testDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Date *
                </label>
                <input
                  type="date"
                  id="testDate"
                  name="testDate"
                  value={formData.testDate}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="testCenter" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Center *
                </label>
                <select
                  id="testCenter"
                  name="testCenter"
                  value={formData.testCenter}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select test center</option>
                  {testCenters.map((center) => (
                    <option key={center} value={center}>
                      {center}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Photo Upload */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Upload className="h-5 w-5 mr-2" />
              Candidate Photo
            </h3>
            <FileUpload
              type="photo"
              onUpload={(url) => setFormData(prev => ({ ...prev, photoUrl: url }))}
              onRemove={() => setFormData(prev => ({ ...prev, photoUrl: '' }))}
              currentFile={formData.photoUrl}
              className="w-full"
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <Link
              href="/admin/candidates"
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {selectedCandidate ? 'Registering...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {selectedCandidate ? 'Register for Test' : 'Create Candidate'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
