import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testRegistrations } from '@/lib/db/schema';
import { eq, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Search for existing candidates by email or passport number (which stores both passport and birth certificate numbers)
    const searchCondition = or(
      eq(candidates.email, query),
      eq(candidates.passportNumber, query)
    );

    // Get candidates with their test registrations
    const candidatesWithRegistrations = await db
      .select({
        // Candidate info
        candidateId: candidates.id,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        dateOfBirth: candidates.dateOfBirth,
        nationality: candidates.nationality,
        passportNumber: candidates.passportNumber,
        photoUrl: candidates.photoUrl,
        photoData: candidates.photoData,
        candidateCreatedAt: candidates.createdAt,
        candidateUpdatedAt: candidates.updatedAt,

        // Registration info
        registrationId: testRegistrations.id,
        candidateNumber: testRegistrations.candidateNumber,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        registrationStatus: testRegistrations.status,
        registrationCreatedAt: testRegistrations.createdAt,
      })
      .from(candidates)
      .leftJoin(testRegistrations, eq(candidates.id, testRegistrations.candidateId))
      .where(searchCondition)
      .orderBy(candidates.fullName, testRegistrations.testDate);

    // Group candidates by their core identity to show unique candidates with their registrations
    const candidateGroups = new Map();

    candidatesWithRegistrations.forEach(row => {
      const key = `${row.email}-${row.passportNumber}`;
      if (!candidateGroups.has(key)) {
        candidateGroups.set(key, {
          id: row.candidateId,
          fullName: row.fullName,
          email: row.email,
          phoneNumber: row.phoneNumber,
          dateOfBirth: row.dateOfBirth,
          nationality: row.nationality,
          passportNumber: row.passportNumber,
          photoUrl: row.photoUrl,
          photoData: row.photoData,
          createdAt: row.candidateCreatedAt,
          updatedAt: row.candidateUpdatedAt,
          testRegistrations: []
        });
      }

      // Add registration if it exists
      if (row.registrationId) {
        candidateGroups.get(key).testRegistrations.push({
          id: row.registrationId,
          candidateNumber: row.candidateNumber,
          testDate: row.testDate,
          testCenter: row.testCenter,
          status: row.registrationStatus,
          createdAt: row.registrationCreatedAt,
        });
      }
    });

    const uniqueCandidates = Array.from(candidateGroups.values());

    return NextResponse.json(uniqueCandidates);
  } catch (error) {
    console.error('Candidate search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
