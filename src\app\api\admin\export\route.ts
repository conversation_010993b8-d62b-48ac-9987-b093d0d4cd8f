import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults, testRegistrations } from '@/lib/db/schema';
import { ilike, eq, or, and, gte, lte } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { filters, format = 'csv' } = await request.json();

    // Build search conditions (same logic as search API)
    const conditions = [];

    if (filters.query) {
      const query = filters.query.trim();
      if (query) {
        switch (filters.searchType) {
          case 'name':
            conditions.push(ilike(candidates.fullName, `%${query}%`));
            break;
          case 'email':
            conditions.push(ilike(candidates.email, `%${query}%`));
            break;
          case 'passport':
            conditions.push(ilike(candidates.passportNumber, `%${query}%`));
            break;
          default:
            conditions.push(
              or(
                ilike(candidates.fullName, `%${query}%`),
                ilike(candidates.email, `%${query}%`),
                ilike(candidates.passportNumber, `%${query}%`)
              )
            );
        }
      }
    }

    if (filters.testCenter) {
      conditions.push(eq(testRegistrations.testCenter, filters.testCenter));
    }

    if (filters.testDateFrom) {
      conditions.push(gte(testRegistrations.testDate, new Date(filters.testDateFrom)));
    }
    if (filters.testDateTo) {
      conditions.push(lte(testRegistrations.testDate, new Date(filters.testDateTo)));
    }

    if (filters.nationality) {
      conditions.push(ilike(candidates.nationality, `%${filters.nationality}%`));
    }

    const whereCondition = conditions.length > 0 ? and(...conditions) : undefined;

    // Get data for export
    const exportData = await db
      .select({
        candidateId: candidates.id,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        passportNumber: candidates.passportNumber,
        nationality: candidates.nationality,
        dateOfBirth: candidates.dateOfBirth,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        candidateNumber: testRegistrations.candidateNumber,
        registrationDate: candidates.createdAt,
        resultId: testResults.id,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        resultStatus: testResults.status,
        certificateGenerated: testResults.certificateGenerated,
        resultCreatedAt: testResults.createdAt,
      })
      .from(candidates)
      .leftJoin(testRegistrations, eq(candidates.id, testRegistrations.candidateId))
      .leftJoin(testResults, eq(testRegistrations.id, testResults.testRegistrationId))
      .where(whereCondition)
      .orderBy(candidates.fullName);

    // Process and filter data
    let processedData = exportData;

    if (filters.hasResults !== 'all') {
      if (filters.hasResults === 'yes') {
        processedData = processedData.filter(r => r.resultId);
      } else if (filters.hasResults === 'no') {
        processedData = processedData.filter(r => !r.resultId);
      }
    }

    if (filters.resultStatus !== 'all' && filters.resultStatus) {
      processedData = processedData.filter(r => r.resultStatus === filters.resultStatus);
    }

    if (filters.bandScoreMin) {
      const minScore = parseFloat(filters.bandScoreMin);
      processedData = processedData.filter(r =>
        r.overallBandScore && parseFloat(r.overallBandScore.toString()) >= minScore
      );
    }

    if (filters.bandScoreMax) {
      const maxScore = parseFloat(filters.bandScoreMax);
      processedData = processedData.filter(r =>
        r.overallBandScore && parseFloat(r.overallBandScore.toString()) <= maxScore
      );
    }

    if (format === 'csv') {
      // Generate CSV
      const headers = [
        'Candidate ID',
        'Full Name',
        'Email',
        'Phone Number',
        'Passport Number',
        'Nationality',
        'Date of Birth',
        'Candidate Number',
        'Test Date',
        'Test Center',
        'Registration Date',
        'Has Results',
        'Result ID',
        'Listening Band Score',
        'Reading Band Score',
        'Writing Band Score',
        'Speaking Band Score',
        'Overall Band Score',
        'Result Status',
        'Certificate Generated',
        'Result Date',
      ];

      const csvRows = [
        headers.join(','),
        ...processedData.map(row => [
          row.candidateId,
          `"${row.fullName}"`,
          row.email,
          row.phoneNumber,
          row.passportNumber,
          `"${row.nationality}"`,
          row.dateOfBirth ? new Date(row.dateOfBirth).toISOString().split('T')[0] : '',
          row.candidateNumber || '',
          row.testDate ? new Date(row.testDate).toISOString().split('T')[0] : '',
          `"${row.testCenter || ''}"`,
          new Date(row.registrationDate).toISOString().split('T')[0],
          row.resultId ? 'Yes' : 'No',
          row.resultId || '',
          row.listeningBandScore || '',
          row.readingBandScore || '',
          row.writingBandScore || '',
          row.speakingBandScore || '',
          row.overallBandScore || '',
          row.resultStatus || '',
          row.certificateGenerated ? 'Yes' : 'No',
          row.resultCreatedAt ? new Date(row.resultCreatedAt).toISOString().split('T')[0] : '',
        ].join(','))
      ];

      const csvContent = csvRows.join('\n');
      const buffer = Buffer.from(csvContent, 'utf-8');

      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="ielts_export_${new Date().toISOString().split('T')[0]}.csv"`,
          'Content-Length': buffer.length.toString(),
        },
      });
    }

    // Default to JSON if format not recognized
    return NextResponse.json({
      data: processedData,
      total: processedData.length,
      exportDate: new Date().toISOString(),
      filters,
    });

  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { error: 'Failed to export data' },
      { status: 500 }
    );
  }
}
