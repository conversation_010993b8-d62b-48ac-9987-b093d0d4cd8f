import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations, users } from '@/lib/db/schema';
import { eq, desc, or, and, count, SQL, gte, lte, ilike } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const testCenter = searchParams.get('testCenter');
    const testDateFrom = searchParams.get('testDateFrom');
    const testDateTo = searchParams.get('testDateTo');
    const minScore = searchParams.get('minScore');
    const maxScore = searchParams.get('maxScore');

    const offset = (page - 1) * limit;

    try {
      // Build where conditions
      const whereConditions: SQL[] = [];

      if (status && status !== 'all') {
        whereConditions.push(eq(testResults.status, status as 'pending' | 'completed' | 'verified'));
      }

      if (search) {
        const searchCondition = or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`)
        );
        if (searchCondition) {
          whereConditions.push(searchCondition);
        }
      }

      // Filter by test center
      if (testCenter) {
        whereConditions.push(eq(testRegistrations.testCenter, testCenter));
      }

      // Filter by test date range
      if (testDateFrom) {
        whereConditions.push(gte(testRegistrations.testDate, new Date(testDateFrom)));
      }
      if (testDateTo) {
        whereConditions.push(lte(testRegistrations.testDate, new Date(testDateTo)));
      }

      // Filter by score range
      if (minScore) {
        whereConditions.push(gte(testResults.overallBandScore, minScore));
      }
      if (maxScore) {
        whereConditions.push(lte(testResults.overallBandScore, maxScore));
      }

      const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(testResults)
        .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(whereClause);

      const total = totalResult[0]?.count || 0;

      // Get results with pagination using new schema structure
      const results = await db
        .select({
          id: testResults.id,
          testRegistrationId: testResults.testRegistrationId,
          listeningBandScore: testResults.listeningBandScore,
          readingBandScore: testResults.readingBandScore,
          writingBandScore: testResults.writingBandScore,
          speakingBandScore: testResults.speakingBandScore,
          overallBandScore: testResults.overallBandScore,
          status: testResults.status,
          certificateGenerated: testResults.certificateGenerated,
          createdAt: testResults.createdAt,
          updatedAt: testResults.updatedAt,
          enteredBy: testResults.enteredBy,
          verifiedBy: testResults.verifiedBy,
          candidate: {
            id: candidates.id,
            fullName: candidates.fullName,
            email: candidates.email,
            passportNumber: candidates.passportNumber,
            nationality: candidates.nationality,
          },
          testRegistration: {
            candidateNumber: testRegistrations.candidateNumber,
            testDate: testRegistrations.testDate,
            testCenter: testRegistrations.testCenter,
          },
          checker: {
            name: users.name,
            email: users.email,
          }
        })
        .from(testResults)
        .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .leftJoin(users, eq(testResults.enteredBy, users.id))
        .where(whereClause)
        .orderBy(desc(testResults.createdAt))
        .limit(limit)
        .offset(offset);

      return NextResponse.json({
        results,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      });

    } catch (dbError) {
      console.warn('Database connection failed, returning empty results:', dbError);

      // Return empty results when database is not available
      return NextResponse.json({
        results: [],
        total: 0,
        page: 1,
        totalPages: 0,
      });
    }

  } catch (error) {
    console.error('Error fetching admin results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.testRegistrationId) {
      return NextResponse.json(
        { error: 'Test Registration ID is required' },
        { status: 400 }
      );
    }

    // Check if test registration exists
    const testRegistration = await db
      .select()
      .from(testRegistrations)
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testRegistrations.id, data.testRegistrationId))
      .limit(1);

    if (!testRegistration.length) {
      return NextResponse.json(
        { error: 'Test registration not found' },
        { status: 404 }
      );
    }

    // Prepare data for database insertion (keep as strings for decimal fields)
    const processedData = {
      testRegistrationId: data.testRegistrationId,

      // Listening scores
      listeningScore: data.listeningScore || null,
      listeningBandScore: data.listeningBandScore || null,

      // Reading scores
      readingScore: data.readingScore || null,
      readingBandScore: data.readingBandScore || null,

      // Writing scores
      writingTask1Score: data.writingTask1Score || null,
      writingTask2Score: data.writingTask2Score || null,
      writingBandScore: data.writingBandScore || null,

      // Speaking scores
      speakingFluencyScore: data.speakingFluencyScore || null,
      speakingLexicalScore: data.speakingLexicalScore || null,
      speakingGrammarScore: data.speakingGrammarScore || null,
      speakingPronunciationScore: data.speakingPronunciationScore || null,
      speakingBandScore: data.speakingBandScore || null,

      // Overall score
      overallBandScore: data.overallBandScore || null,

      // Metadata
      status: data.status || 'pending' as const,
      enteredBy: session.user?.id,
    };

    // Insert the test result
    const result = await db
      .insert(testResults)
      .values(processedData)
      .returning();

    return NextResponse.json(result[0], { status: 201 });

  } catch (error) {
    console.error('Error creating test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
