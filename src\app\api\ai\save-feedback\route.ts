import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { aiFeedback } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { resultId, feedback } = await request.json();

    if (!resultId || !feedback) {
      return NextResponse.json(
        { error: 'Result ID and feedback are required' },
        { status: 400 }
      );
    }

    // Check if feedback already exists for this result
    const existingFeedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, resultId))
      .limit(1);

    if (existingFeedback.length > 0) {
      // Update existing feedback
      const updatedFeedback = await db
        .update(aiFeedback)
        .set({
          overallFeedback: feedback.overallFeedback || feedback.overallAssessment,
          listeningFeedback: feedback.listeningFeedback || feedback.specificRecommendations?.listening,
          readingFeedback: feedback.readingFeedback || feedback.specificRecommendations?.reading,
          writingFeedback: feedback.writingFeedback || feedback.specificRecommendations?.writing,
          speakingFeedback: feedback.speakingFeedback || feedback.specificRecommendations?.speaking,
          studyRecommendations: feedback.studyRecommendations,
          strengths: feedback.strengths,
          weaknesses: feedback.weaknesses || feedback.areasForImprovement,
          studyPlan: feedback.studyPlan,
        })
        .where(eq(aiFeedback.testResultId, resultId))
        .returning();

      return NextResponse.json(updatedFeedback[0]);
    } else {
      // Create new feedback
      const newFeedback = await db
        .insert(aiFeedback)
        .values({
          testResultId: resultId,
          overallFeedback: feedback.overallFeedback || feedback.overallAssessment,
          listeningFeedback: feedback.listeningFeedback || feedback.specificRecommendations?.listening,
          readingFeedback: feedback.readingFeedback || feedback.specificRecommendations?.reading,
          writingFeedback: feedback.writingFeedback || feedback.specificRecommendations?.writing,
          speakingFeedback: feedback.speakingFeedback || feedback.specificRecommendations?.speaking,
          studyRecommendations: feedback.studyRecommendations,
          strengths: feedback.strengths,
          weaknesses: feedback.weaknesses || feedback.areasForImprovement,
          studyPlan: feedback.studyPlan,
        })
        .returning();

      return NextResponse.json(newFeedback[0], { status: 201 });
    }
  } catch (error) {
    console.error('Error saving AI feedback:', error);
    return NextResponse.json(
      { error: 'Failed to save feedback' },
      { status: 500 }
    );
  }
}
