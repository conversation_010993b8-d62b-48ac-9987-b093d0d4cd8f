import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // PUBLIC ACCESS - No authentication required for progress data
    const { id: candidateId } = await params;

    // Get candidate details
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!candidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Get all test results for this candidate
    const testHistory = await db
      .select({
        id: testResults.id,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        candidateNumber: testRegistrations.candidateNumber,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        certificateGenerated: testResults.certificateGenerated,
        createdAt: testResults.createdAt,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .where(eq(testRegistrations.candidateId, candidateId))
      .orderBy(desc(testRegistrations.testDate));

    if (testHistory.length === 0) {
      return NextResponse.json(
        { error: 'No test history found for this candidate' },
        { status: 404 }
      );
    }

    // Calculate progress metrics
    const allScores = testHistory
      .map(test => test.overallBandScore)
      .filter(score => score !== null)
      .map(score => parseFloat(score as string));

    const averageScore = allScores.length > 0 
      ? Math.round((allScores.reduce((sum, score) => sum + score, 0) / allScores.length) * 2) / 2
      : 0;

    const highestScore = allScores.length > 0 ? Math.max(...allScores) : 0;
    const lowestScore = allScores.length > 0 ? Math.min(...allScores) : 0;

    // Determine improvement trend
    let improvementTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (allScores.length >= 2) {
      const recentScores = allScores.slice(0, 3); // Most recent 3 scores
      const firstScore = recentScores[recentScores.length - 1];
      const lastScore = recentScores[0];
      
      if (lastScore > firstScore + 0.5) {
        improvementTrend = 'improving';
      } else if (lastScore < firstScore - 0.5) {
        improvementTrend = 'declining';
      }
    }

    // Prepare score progression data for charts
    const scoreProgression = {
      listening: testHistory.map(test => test.listeningBandScore ? parseFloat(test.listeningBandScore as string) : null).reverse(),
      reading: testHistory.map(test => test.readingBandScore ? parseFloat(test.readingBandScore as string) : null).reverse(),
      writing: testHistory.map(test => test.writingBandScore ? parseFloat(test.writingBandScore as string) : null).reverse(),
      speaking: testHistory.map(test => test.speakingBandScore ? parseFloat(test.speakingBandScore as string) : null).reverse(),
      overall: testHistory.map(test => test.overallBandScore ? parseFloat(test.overallBandScore as string) : null).reverse(),
      dates: testHistory.map(test => new Date(test.testDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })).reverse()
    };

    // Format test history for response
    const formattedTestHistory = testHistory.map(test => ({
      id: test.id,
      testDate: test.testDate.toISOString(),
      testCenter: test.testCenter,
      listeningBandScore: test.listeningBandScore ? parseFloat(test.listeningBandScore as string) : null,
      readingBandScore: test.readingBandScore ? parseFloat(test.readingBandScore as string) : null,
      writingBandScore: test.writingBandScore ? parseFloat(test.writingBandScore as string) : null,
      speakingBandScore: test.speakingBandScore ? parseFloat(test.speakingBandScore as string) : null,
      overallBandScore: test.overallBandScore ? parseFloat(test.overallBandScore as string) : null,
      status: test.status as 'pending' | 'completed' | 'verified',
      certificateGenerated: test.certificateGenerated || false
    }));

    const progressData = {
      candidateId: candidate[0].id,
      fullName: candidate[0].fullName,
      nationality: candidate[0].nationality,
      photoUrl: candidate[0].photoUrl,
      totalTests: testHistory.length,
      averageScore,
      highestScore,
      lowestScore,
      improvementTrend,
      testHistory: formattedTestHistory,
      currentResult: formattedTestHistory[0], // Most recent test
      scoreProgression
    };

    return NextResponse.json(progressData);
  } catch (error) {
    console.error('Error fetching candidate progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
