import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query, searchType } = await request.json();

    if (!query || !searchType) {
      return NextResponse.json(
        { error: 'Query and search type are required' },
        { status: 400 }
      );
    }

    // RESTRICTED SEARCH: Only search passport number field (which stores both passport and birth certificate numbers)
    // Ignore searchType parameter and always search passport number field
    const searchCondition = eq(candidates.passportNumber, query);

    // Get candidates with their test registrations and results (if any)
    const candidatesWithResults = await db
      .select({
        id: candidates.id,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        dateOfBirth: candidates.dateOfBirth,
        nationality: candidates.nationality,
        passportNumber: candidates.passportNumber,
        candidateNumber: testRegistrations.candidateNumber,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        photoUrl: candidates.photoUrl,
        registrationId: testRegistrations.id,
        resultId: testResults.id,
        hasResults: testResults.id,
      })
      .from(candidates)
      .leftJoin(testRegistrations, eq(candidates.id, testRegistrations.candidateId))
      .leftJoin(testResults, eq(testResults.testRegistrationId, testRegistrations.id))
      .where(searchCondition)
      .orderBy(candidates.fullName);

    // Transform the results to group by candidate and include all test registrations
    const candidateMap = new Map();

    candidatesWithResults.forEach((row) => {
      if (!candidateMap.has(row.id)) {
        candidateMap.set(row.id, {
          id: row.id,
          fullName: row.fullName,
          email: row.email,
          phoneNumber: row.phoneNumber,
          dateOfBirth: row.dateOfBirth,
          nationality: row.nationality,
          passportNumber: row.passportNumber,
          photoUrl: row.photoUrl,
          testRegistrations: [],
        });
      }

      const candidate = candidateMap.get(row.id);
      if (row.registrationId) {
        candidate.testRegistrations.push({
          id: row.registrationId,
          candidateNumber: row.candidateNumber,
          testDate: row.testDate,
          testCenter: row.testCenter,
          hasResults: !!row.hasResults,
          resultId: row.resultId,
        });
      }
    });

    const results = Array.from(candidateMap.values());

    return NextResponse.json(results);
  } catch (error) {
    console.error('Candidate search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
