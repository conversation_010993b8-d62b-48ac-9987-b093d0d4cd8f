import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateCertificate } from '@/lib/certificate-generator';
import { generateCertificateSerial } from '@/lib/utils/certificate';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // PUBLIC ACCESS - No authentication required for certificate generation
    const { id: resultId } = await params;

    // Get test result with candidate and registration info
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        registration: testRegistrations,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const { testResult, candidate, registration } = result[0];

    // Check if result is completed or verified
    if (testResult.status !== 'completed' && testResult.status !== 'verified') {
      return NextResponse.json(
        { error: 'Certificate can only be generated for completed results' },
        { status: 403 }
      );
    }

    // Check if all required scores are available
    if (!testResult.overallBandScore) {
      return NextResponse.json(
        { error: 'Overall band score is required for certificate generation' },
        { status: 400 }
      );
    }

    // Check if certificate already exists
    if (testResult.certificateGenerated && testResult.certificateSerial) {
      return NextResponse.json({
        success: true,
        message: 'Certificate already exists for this result',
        certificateSerial: testResult.certificateSerial,
        downloadUrl: `/api/certificate/${resultId}`,
      });
    }

    // Generate certificate serial if not exists
    let certificateSerial = testResult.certificateSerial;
    if (!certificateSerial) {
      certificateSerial = generateCertificateSerial();
    }

    // Generate the certificate PDF (this validates the generation process)
    await generateCertificate(testResult, candidate, registration);

    // Update the test result to mark certificate as generated
    await db
      .update(testResults)
      .set({
        certificateGenerated: true,
        certificateSerial: certificateSerial,
        updatedAt: new Date()
      })
      .where(eq(testResults.id, resultId));

    return NextResponse.json({
      success: true,
      message: 'Certificate generated successfully',
      certificateSerial: certificateSerial,
      downloadUrl: `/api/certificate/${resultId}`,
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: 'Failed to generate certificate. Please try again.' },
      { status: 500 }
    );
  }
}
