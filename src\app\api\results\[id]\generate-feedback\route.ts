import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations, aiFeedback } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateAIFeedback } from '@/lib/ai-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // PUBLIC ACCESS - No authentication required for AI feedback generation
    const { id: resultId } = await params;

    // Get test result with candidate info using new schema structure
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        testRegistration: testRegistrations,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const { testResult, candidate } = result[0];

    // Check if result is completed or verified
    if (testResult.status !== 'completed' && testResult.status !== 'verified') {
      return NextResponse.json(
        { error: 'AI feedback can only be generated for completed results' },
        { status: 403 }
      );
    }

    // Check if all required scores are available
    if (!testResult.overallBandScore) {
      return NextResponse.json(
        { error: 'Overall band score is required for feedback generation' },
        { status: 400 }
      );
    }

    // Check if feedback already exists
    const existingFeedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, resultId))
      .limit(1);

    if (existingFeedback.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'AI feedback already exists for this result',
        feedback: existingFeedback[0],
      });
    }

    // Generate AI feedback
    const feedbackData = await generateAIFeedback(testResult, candidate);

    // Save the feedback to database
    const newFeedback = await db
      .insert(aiFeedback)
      .values({
        testResultId: resultId,
        listeningFeedback: feedbackData.listeningFeedback,
        readingFeedback: feedbackData.readingFeedback,
        writingFeedback: feedbackData.writingFeedback,
        speakingFeedback: feedbackData.speakingFeedback,
        overallFeedback: feedbackData.overallFeedback,
        studyRecommendations: feedbackData.recommendations,
        strengths: feedbackData.strengths,
        weaknesses: feedbackData.weaknesses,
        studyPlan: feedbackData.studyPlan,
      })
      .returning();

    // Update test result to mark AI feedback as generated
    await db
      .update(testResults)
      .set({
        aiFeedbackGenerated: true,
        updatedAt: new Date()
      })
      .where(eq(testResults.id, resultId));

    return NextResponse.json({
      success: true,
      message: 'AI feedback generated successfully',
      feedback: newFeedback[0],
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating AI feedback:', error);

    // Handle specific AI service errors
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI service configuration error. Please contact administrator.' },
          { status: 500 }
        );
      }
      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'AI service temporarily unavailable. Please try again later.' },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate AI feedback. Please try again.' },
      { status: 500 }
    );
  }
}
