import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { candidates, testResults, testRegistrations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // RESTRICTED SEARCH: Only search passport number field (which stores both passport and birth certificate numbers)
    // Updated to use the new schema with test_registrations table
    const results = await db
      .select({
        id: testResults.id,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        certificateGenerated: testResults.certificateGenerated,
        candidate: {
          fullName: candidates.fullName,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
        },
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        // Only search by passport number field (exact match)
        eq(candidates.passportNumber, query)
      );

    return NextResponse.json(results);
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
