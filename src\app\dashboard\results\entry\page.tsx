'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { AlertCircle, Check, RefreshCw, Calendar, Filter } from 'lucide-react';
import { getDefaultQuickEntryDate, getTestDateOptions, formatTestDate, formatDateForInput } from '@/lib/utils/dateHelpers';

// Types for the Quick Entry system
interface Candidate {
  id: string;
  candidateNumber: string;
  fullName: string;
  email: string;
  testDate: string;
  nationality?: string;
  photoUrl?: string;
  hasResult: boolean;
  result?: {
    id: string;
    listeningBandScore: number | string | null;
    readingBandScore: number | string | null;
    writingTask1Score: number | string | null;
    writingTask2Score: number | string | null;
    writingBandScore: number | string | null;
    speakingBandScore: number | string | null;
    overallBandScore: number | string | null;
    status: 'pending' | 'completed' | 'verified';
  };
}

interface BandScores {
  [candidateId: string]: {
    listening: number | null;
    reading: number | null;
    writingTask1: number | null;
    writingTask2: number | null;
    speaking: number | null;
  };
}

interface SaveStatus {
  [candidateId: string]: {
    [skill: string]: 'idle' | 'saving' | 'saved' | 'error';
  };
}

// Band score options (0.0 to 9.0 in 0.5 increments)
const BAND_SCORE_OPTIONS = [
  0.0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5,
  5.0, 5.5, 6.0, 6.5, 7.0, 7.5, 8.0, 8.5, 9.0
];

export default function QuickEntryPage() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [bandScores, setBandScores] = useState<BandScores>({});
  const [saveStatus, setSaveStatus] = useState<SaveStatus>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [selectedTestDate, setSelectedTestDate] = useState<string>(formatDateForInput(getDefaultQuickEntryDate()));
  const [availableTestDates, setAvailableTestDates] = useState<Date[]>([]);

  // Initialize available test dates on component mount
  useEffect(() => {
    const testDates = getTestDateOptions(4, 8); // 4 weeks back, 8 weeks forward
    setAvailableTestDates(testDates);
  }, []);

  const fetchCandidates = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/checker/candidates?includeResults=true&testDate=${selectedTestDate}`);

      if (!response.ok) {
        throw new Error('Failed to fetch candidates');
      }

      const data = await response.json();
      setCandidates(data.candidates || []);

      // Initialize band scores from existing results
      const initialScores: BandScores = {};
      const initialSaveStatus: SaveStatus = {};

      data.candidates?.forEach((candidate: Candidate) => {
        initialScores[candidate.id] = {
          listening: candidate.result?.listeningBandScore ?
            parseFloat(candidate.result.listeningBandScore.toString()) : null,
          reading: candidate.result?.readingBandScore ?
            parseFloat(candidate.result.readingBandScore.toString()) : null,
          writingTask1: candidate.result?.writingTask1Score ?
            parseFloat(candidate.result.writingTask1Score.toString()) : null,
          writingTask2: candidate.result?.writingTask2Score ?
            parseFloat(candidate.result.writingTask2Score.toString()) : null,
          speaking: candidate.result?.speakingBandScore ?
            parseFloat(candidate.result.speakingBandScore.toString()) : null,
        };

        initialSaveStatus[candidate.id] = {
          listening: 'idle',
          reading: 'idle',
          writingTask1: 'idle',
          writingTask2: 'idle',
          speaking: 'idle',
        };
      });

      setBandScores(initialScores);
      setSaveStatus(initialSaveStatus);
    } catch (error) {
      console.error('Error fetching candidates:', error);
      setError('Failed to load candidates. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [selectedTestDate]);

  // Fetch candidates when component mounts or test date changes
  useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  // Calculate overall band score
  const calculateOverallBandScore = (scores: BandScores[string]) => {
    const { listening, reading, writingTask1, writingTask2, speaking } = scores;

    // Calculate writing band score from tasks
    let writingBand = null;
    if (writingTask1 !== null && writingTask2 !== null) {
      writingBand = (writingTask1 + writingTask2) / 2;
    }

    const validScores = [listening, reading, writingBand, speaking].filter(
      score => score !== null
    ) as number[];

    if (validScores.length === 4) {
      const average = validScores.reduce((sum, score) => sum + score, 0) / 4;
      return Math.round(average * 2) / 2; // Round to nearest 0.5
    }

    return null;
  };

  // Update band score for a specific candidate and skill
  const updateBandScore = (candidateId: string, skill: keyof BandScores[string], value: number | null) => {
    setBandScores(prev => ({
      ...prev,
      [candidateId]: {
        ...prev[candidateId],
        [skill]: value
      }
    }));

    // Auto-save after a short delay
    setTimeout(() => {
      saveBandScore(candidateId, skill, value);
    }, 500);
  };

  // Save band score to database
  const saveBandScore = async (candidateId: string, skill: keyof BandScores[string], value: number | null) => {
    const candidate = candidates.find(c => c.id === candidateId);
    if (!candidate) {
      console.error('Candidate not found:', candidateId);
      return;
    }

    // Update save status to saving
    setSaveStatus(prev => ({
      ...prev,
      [candidateId]: {
        ...prev[candidateId],
        [skill]: 'saving'
      }
    }));

    try {
      // Get current scores from state (use fresh state)
      const currentScores = bandScores[candidateId];
      if (!currentScores) {
        console.error('No scores found for candidate:', candidateId);
        return;
      }

      // Create updated scores with the new value
      const updatedScores = {
        ...currentScores,
        [skill]: value
      };

      const overallBandScore = calculateOverallBandScore(updatedScores);

      // Prepare the data for API - convert numbers to strings for decimal fields
      const resultData = {
        candidateId,
        listeningBandScore: updatedScores.listening?.toString() || null,
        readingBandScore: updatedScores.reading?.toString() || null,
        writingTask1Score: updatedScores.writingTask1?.toString() || null,
        writingTask2Score: updatedScores.writingTask2?.toString() || null,
        writingBandScore: updatedScores.writingTask1 && updatedScores.writingTask2
          ? ((updatedScores.writingTask1 + updatedScores.writingTask2) / 2).toString()
          : null,
        speakingBandScore: updatedScores.speaking?.toString() || null,
        overallBandScore: overallBandScore?.toString() || null,
        status: 'completed' as const,
      };

      console.log('Saving data:', { candidateId, skill, value, resultData });

      const url = candidate.hasResult
        ? `/api/checker/results/${candidate.result?.id}`
        : '/api/checker/results';

      const method = candidate.hasResult ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resultData),
      });

      if (response.ok) {
        const responseData = await response.json();
        console.log('Save successful:', responseData);

        // Update save status to saved
        setSaveStatus(prev => ({
          ...prev,
          [candidateId]: {
            ...prev[candidateId],
            [skill]: 'saved'
          }
        }));

        // Update candidate data optimistically (no full refresh)
        setCandidates(prev => prev.map(c => {
          if (c.id === candidateId) {
            return {
              ...c,
              hasResult: true,
              result: {
                ...c.result,
                id: responseData.id || c.result?.id,
                listeningBandScore: updatedScores.listening,
                readingBandScore: updatedScores.reading,
                writingTask1Score: updatedScores.writingTask1,
                writingTask2Score: updatedScores.writingTask2,
                writingBandScore: updatedScores.writingTask1 && updatedScores.writingTask2
                  ? (updatedScores.writingTask1 + updatedScores.writingTask2) / 2
                  : c.result?.writingBandScore || null,
                speakingBandScore: updatedScores.speaking,
                overallBandScore: overallBandScore,
                status: 'completed' as const,
              }
            };
          }
          return c;
        }));

        // Show success message
        const skillName = skill === 'writingTask1' ? 'Writing Task 1' :
                         skill === 'writingTask2' ? 'Writing Task 2' :
                         skill.charAt(0).toUpperCase() + skill.slice(1);

        setSuccessMessage(`${skillName} score saved for ${candidate.candidateNumber}`);
        setTimeout(() => setSuccessMessage(null), 3000);

        // Clear saved status after 2 seconds
        setTimeout(() => {
          setSaveStatus(prev => ({
            ...prev,
            [candidateId]: {
              ...prev[candidateId],
              [skill]: 'idle'
            }
          }));
        }, 2000);

      } else {
        const errorData = await response.json();
        console.error('Save failed:', errorData);
        throw new Error(errorData.error || 'Failed to save score');
      }
    } catch (error) {
      console.error('Error saving score:', error);

      // Update save status to error
      setSaveStatus(prev => ({
        ...prev,
        [candidateId]: {
          ...prev[candidateId],
          [skill]: 'error'
        }
      }));

      setError(`Failed to save ${skill} score. Please try again.`);
      setTimeout(() => setError(null), 5000);

      // Clear error status after 3 seconds
      setTimeout(() => {
        setSaveStatus(prev => ({
          ...prev,
          [candidateId]: {
            ...prev[candidateId],
            [skill]: 'idle'
          }
        }));
      }, 3000);
    }
  };

  // Band Score Selector Component
  const BandScoreSelector = ({
    candidateId,
    skill,
    value,
    disabled = false
  }: {
    candidateId: string;
    skill: keyof BandScores[string];
    value: number | null;
    disabled?: boolean;
  }) => {
    const status = saveStatus[candidateId]?.[skill] || 'idle';

    const getStatusColor = () => {
      switch (status) {
        case 'saving': return 'border-yellow-300 bg-yellow-50';
        case 'saved': return 'border-green-300 bg-green-50';
        case 'error': return 'border-red-300 bg-red-50';
        default: return 'border-gray-300 bg-white';
      }
    };

    const getStatusIcon = () => {
      switch (status) {
        case 'saving': return <RefreshCw className="h-3 w-3 animate-spin text-yellow-600" />;
        case 'saved': return <Check className="h-3 w-3 text-green-600" />;
        case 'error': return <AlertCircle className="h-3 w-3 text-red-600" />;
        default: return null;
      }
    };

    return (
      <div className="relative">
        <select
          value={value || ''}
          onChange={(e) => {
            const newValue = e.target.value ? parseFloat(e.target.value) : null;
            updateBandScore(candidateId, skill, newValue);
          }}
          disabled={disabled}
          className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${getStatusColor()} ${
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
        >
          <option value="">Select</option>
          {BAND_SCORE_OPTIONS.map(score => (
            <option key={score} value={score}>
              {score.toFixed(1)}
            </option>
          ))}
        </select>

        {/* Status indicator */}
        {getStatusIcon() && (
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            {getStatusIcon()}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-lg text-gray-600">Loading candidates...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quick Entry</h1>
          <p className="text-gray-600 mt-1">Fast band assignment for test checkers</p>
        </div>
        <button
          onClick={fetchCandidates}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Test Date Selector */}
      <div className="bg-white shadow-lg rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Test Date Filter</h2>
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <Filter className="h-4 w-4 mr-1" />
            {candidates.length} candidate{candidates.length !== 1 ? 's' : ''} found
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="testDate" className="block text-sm font-medium text-gray-700 mb-2">
              Select Test Date
            </label>
            <select
              id="testDate"
              value={selectedTestDate}
              onChange={(e) => setSelectedTestDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {availableTestDates.map((date) => {
                const dateStr = formatDateForInput(date);
                const isToday = dateStr === formatDateForInput(new Date());
                const isPast = date < new Date();

                return (
                  <option key={dateStr} value={dateStr}>
                    {formatTestDate(date)}
                    {isToday && ' (Today)'}
                    {isPast && !isToday && ' (Past)'}
                  </option>
                );
              })}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setSelectedTestDate(formatDateForInput(getDefaultQuickEntryDate()))}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Reset to Default
            </button>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-700">
            <strong>Selected:</strong> {formatTestDate(new Date(selectedTestDate))}
            {candidates.length === 0 && !loading && (
              <span className="block mt-1 text-blue-600">
                No candidates registered for this test date.
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center">
          <Check className="h-4 w-4 mr-2" />
          {successMessage}
        </div>
      )}

      {/* Candidates Count */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-800 font-medium">
          Total Candidates: {candidates.length}
        </p>
      </div>

      {/* Quick Entry Table */}
      <div className="bg-white shadow-lg rounded-xl overflow-hidden">
        {candidates.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 text-lg">No candidates found</p>
            <p className="text-gray-400 text-sm mt-2">
              Add candidates from the admin panel to start entering results
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-gray-200">
              <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-700 uppercase tracking-wider w-1/4">
                    Candidate Information
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    🎧 Listening
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    📖 Reading
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    ✍️ Writing T1
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    ✍️ Writing T2
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    🗣️ Speaking
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    🎯 Overall
                  </th>
                  <th className="px-4 py-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider w-1/8">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {candidates.map((candidate, index) => {
                  const scores = bandScores[candidate.id] || {
                    listening: null,
                    reading: null,
                    writingTask1: null,
                    writingTask2: null,
                    speaking: null,
                  };

                  const overallScore = calculateOverallBandScore(scores);

                  return (
                    <tr
                      key={candidate.id}
                      className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}
                    >
                      {/* Candidate Information */}
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-3">
                          {candidate.photoUrl && (
                            <div className="relative h-10 w-10 rounded-full overflow-hidden border-2 border-gray-200">
                              <Image
                                src={candidate.photoUrl}
                                alt={candidate.fullName}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {candidate.fullName}
                            </div>
                            <div className="text-sm text-gray-500">
                              #{candidate.candidateNumber}
                            </div>
                            <div className="text-xs text-gray-400">
                              {new Date(candidate.testDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Listening Score */}
                      <td className="px-4 py-4 text-center">
                        <BandScoreSelector
                          candidateId={candidate.id}
                          skill="listening"
                          value={scores.listening}
                        />
                      </td>

                      {/* Reading Score */}
                      <td className="px-4 py-4 text-center">
                        <BandScoreSelector
                          candidateId={candidate.id}
                          skill="reading"
                          value={scores.reading}
                        />
                      </td>

                      {/* Writing Task 1 Score */}
                      <td className="px-4 py-4 text-center">
                        <BandScoreSelector
                          candidateId={candidate.id}
                          skill="writingTask1"
                          value={scores.writingTask1}
                        />
                      </td>

                      {/* Writing Task 2 Score */}
                      <td className="px-4 py-4 text-center">
                        <BandScoreSelector
                          candidateId={candidate.id}
                          skill="writingTask2"
                          value={scores.writingTask2}
                        />
                      </td>

                      {/* Speaking Score */}
                      <td className="px-4 py-4 text-center">
                        <BandScoreSelector
                          candidateId={candidate.id}
                          skill="speaking"
                          value={scores.speaking}
                        />
                      </td>

                      {/* Overall Score */}
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          overallScore
                            ? overallScore >= 7
                              ? 'bg-green-100 text-green-800'
                              : overallScore >= 5.5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-500'
                        }`}>
                          {overallScore ? overallScore.toFixed(1) : 'Pending'}
                        </div>
                      </td>

                      {/* Status */}
                      <td className="px-4 py-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          candidate.result?.status === 'verified'
                            ? 'bg-green-100 text-green-800'
                            : candidate.result?.status === 'completed'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}>
                          {candidate.result?.status || 'pending'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}