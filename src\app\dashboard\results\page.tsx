'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ResultsEntryPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to Quick Entry page as this page is obsolete
    router.replace('/dashboard/results/entry');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to Quick Entry...</p>
      </div>
    </div>
  );
}
