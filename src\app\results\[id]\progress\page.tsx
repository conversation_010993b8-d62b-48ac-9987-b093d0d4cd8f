'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLef<PERSON>,
  TrendingUp,
  TrendingDown,
  Target,
  Award,
  BarChart3,
  LineChart,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import HorizontalNavigationMenu from '@/components/results/HorizontalNavigationMenu';
import InteractiveProgressChart from '@/components/charts/InteractiveProgressChart';
import SingleTestMotivation from '@/components/results/SingleTestMotivation';

interface HistoricalTestResult {
  id: string;
  testDate: string;
  testCenter: string;
  listeningBandScore: number | null;
  readingBandScore: number | null;
  writingBandScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateGenerated: boolean;
}

interface CandidateProgress {
  candidateId: string;
  fullName: string;
  nationality: string;
  photoUrl: string | null;
  totalTests: number;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  improvementTrend: 'improving' | 'declining' | 'stable';
  testHistory: HistoricalTestResult[];
  currentResult: HistoricalTestResult;
  scoreProgression: {
    listening: number[];
    reading: number[];
    writing: number[];
    speaking: number[];
    overall: number[];
    dates: string[];
  };
}

export default function ProgressPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [progressData, setProgressData] = useState<CandidateProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchProgressData = useCallback(async () => {
    try {
      // First fetch the current result to get candidate info
      const resultResponse = await fetch(`/api/results/${resultId}`);
      if (!resultResponse.ok) {
        throw new Error('Result not found');
      }
      const currentResult = await resultResponse.json();

      // Then fetch historical progress data for this candidate
      const progressResponse = await fetch(`/api/candidates/${currentResult.candidate.id}/progress`);
      if (progressResponse.ok) {
        const progressData = await progressResponse.json();
        setProgressData(progressData);
      } else {
        // If no historical data available (single test), create special handling
        const singleTestData = createSingleTestData(currentResult);
        setProgressData(singleTestData);
      }
    } catch (error) {
      console.error('Error fetching progress data:', error);
      setError('Failed to load progress data');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    fetchProgressData();
  }, [fetchProgressData]);

  // Create single test data for new candidates
  const createSingleTestData = (currentResult: {
    id: string;
    candidate: { id: string; fullName: string; nationality: string; photoUrl: string | null };
    testRegistration: { testDate: string; testCenter: string };
    listeningBandScore: number | null;
    readingBandScore: number | null;
    writingBandScore: number | null;
    speakingBandScore: number | null;
    overallBandScore: number | null;
    status: 'pending' | 'completed' | 'verified';
    certificateGenerated: boolean;
  }): CandidateProgress => {
    return {
      candidateId: currentResult.candidate.id,
      fullName: currentResult.candidate.fullName,
      nationality: currentResult.candidate.nationality,
      photoUrl: currentResult.candidate.photoUrl,
      totalTests: 1,
      averageScore: currentResult.overallBandScore || 0,
      highestScore: currentResult.overallBandScore || 0,
      lowestScore: currentResult.overallBandScore || 0,
      improvementTrend: 'stable' as const,
      testHistory: [{
        id: currentResult.id,
        testDate: currentResult.testRegistration.testDate,
        testCenter: currentResult.testRegistration.testCenter,
        listeningBandScore: currentResult.listeningBandScore,
        readingBandScore: currentResult.readingBandScore,
        writingBandScore: currentResult.writingBandScore,
        speakingBandScore: currentResult.speakingBandScore,
        overallBandScore: currentResult.overallBandScore,
        status: currentResult.status,
        certificateGenerated: currentResult.certificateGenerated
      }],
      currentResult: {
        id: currentResult.id,
        testDate: currentResult.testRegistration.testDate,
        testCenter: currentResult.testRegistration.testCenter,
        listeningBandScore: currentResult.listeningBandScore,
        readingBandScore: currentResult.readingBandScore,
        writingBandScore: currentResult.writingBandScore,
        speakingBandScore: currentResult.speakingBandScore,
        overallBandScore: currentResult.overallBandScore,
        status: currentResult.status,
        certificateGenerated: currentResult.certificateGenerated
      },
      scoreProgression: {
        listening: [currentResult.listeningBandScore || 0],
        reading: [currentResult.readingBandScore || 0],
        writing: [currentResult.writingBandScore || 0],
        speaking: [currentResult.speakingBandScore || 0],
        overall: [currentResult.overallBandScore || 0],
        dates: [new Date(currentResult.testRegistration.testDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })]
      }
    };
  };



  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-5 w-5 text-success-500" />;
      case 'declining':
        return <TrendingDown className="h-5 w-5 text-error-500" />;
      default:
        return <Target className="h-5 w-5 text-warning-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving':
        return 'text-success-600 bg-success-50';
      case 'declining':
        return 'text-error-600 bg-error-50';
      default:
        return 'text-warning-600 bg-warning-50';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center animate-fade-in">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-t-primary-300 animate-pulse mx-auto"></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Progress History</h3>
          <p className="text-muted-foreground">Analyzing your IELTS journey...</p>
        </div>
      </div>
    );
  }

  if (error || !progressData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8 animate-fade-in">
          <div className="p-4 bg-red-100 rounded-full w-fit mx-auto mb-6">
            <AlertCircle className="h-12 w-12 text-red-500" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-3">Unable to Load Progress</h3>
          <p className="text-muted-foreground mb-8 leading-relaxed">{error || 'Progress data could not be found. Please check your result ID and try again.'}</p>
          <Link
            href="/search"
            className="btn-primary inline-flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-white/20 shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link
                href="/search"
                className="flex items-center text-primary-600 hover:text-primary-700 mr-6 transition-colors duration-200 focus-ring rounded-md p-2 -m-2"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="font-medium">Back to Search</span>
              </Link>
              <div className="flex items-center">
                <div className="p-3 bg-primary-100 rounded-xl mr-4">
                  <TrendingUp className="h-8 w-8 text-primary-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Historical Progress</h1>
                  <p className="text-muted-foreground">Track your IELTS journey over time</p>
                </div>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-muted-foreground">Total Tests</div>
                <div className="font-bold text-lg text-primary-600">{progressData.totalTests}</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Horizontal Navigation */}
      <HorizontalNavigationMenu
        resultId={resultId}
        completionStatus={{
          overview: true,
          progress: true,
          feedback: progressData.currentResult.status === 'completed',
          certificate: progressData.currentResult.certificateGenerated
        }}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Single Test Motivation for new candidates */}
          {progressData.totalTests === 1 ? (
            <SingleTestMotivation
              result={{
                overallBandScore: progressData.currentResult.overallBandScore,
                listeningBandScore: progressData.currentResult.listeningBandScore,
                readingBandScore: progressData.currentResult.readingBandScore,
                writingBandScore: progressData.currentResult.writingBandScore,
                speakingBandScore: progressData.currentResult.speakingBandScore
              }}
              candidateName={progressData.fullName}
            />
          ) : (
            <>
              {/* Progress Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Average Score Card */}
            <div className="card-elevated animate-slide-up">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary-600">{progressData.averageScore}</div>
                    <div className="text-xs text-muted-foreground">Average Score</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Across {progressData.totalTests} test{progressData.totalTests !== 1 ? 's' : ''}
                </div>
              </div>
            </div>

            {/* Highest Score Card */}
            <div className="card-elevated animate-slide-up" style={{ animationDelay: '100ms' }}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-success-100 rounded-lg">
                    <Award className="h-6 w-6 text-success-600" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-success-600">{progressData.highestScore}</div>
                    <div className="text-xs text-muted-foreground">Highest Score</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Personal best achievement
                </div>
              </div>
            </div>

            {/* Improvement Trend Card */}
            <div className="card-elevated animate-slide-up" style={{ animationDelay: '200ms' }}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-2 rounded-lg ${getTrendColor(progressData.improvementTrend).includes('success') ? 'bg-success-100' : getTrendColor(progressData.improvementTrend).includes('error') ? 'bg-error-100' : 'bg-warning-100'}`}>
                    {getTrendIcon(progressData.improvementTrend)}
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-semibold px-2 py-1 rounded-full ${getTrendColor(progressData.improvementTrend)}`}>
                      {progressData.improvementTrend.charAt(0).toUpperCase() + progressData.improvementTrend.slice(1)}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Recent performance trend
                </div>
              </div>
            </div>

            {/* Score Range Card */}
            <div className="card-elevated animate-slide-up" style={{ animationDelay: '300ms' }}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <LineChart className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-purple-600">
                      {progressData.lowestScore} - {progressData.highestScore}
                    </div>
                    <div className="text-xs text-muted-foreground">Score Range</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  Performance bandwidth
                </div>
              </div>
            </div>
          </div>

          {/* Interactive Score Progression Chart */}
          <div className="animate-slide-up" style={{ animationDelay: '400ms' }}>
            <InteractiveProgressChart
              scoreProgression={progressData.scoreProgression}
              showPrediction={true}
            />
          </div>

          {/* Historical Test Results */}
          <div className="card-elevated animate-slide-up" style={{ animationDelay: '500ms' }}>
            <div className="p-6">
              <div className="flex items-center mb-6">
                <FileText className="h-5 w-5 text-primary-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Test History</h2>
                <span className="ml-auto text-sm text-muted-foreground">
                  {progressData.totalTests} test{progressData.totalTests !== 1 ? 's' : ''} completed
                </span>
              </div>

              <div className="space-y-4">
                {progressData.testHistory.map((test, index) => (
                  <div
                    key={test.id}
                    className={`p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${
                      test.id === progressData.currentResult.id
                        ? 'border-primary-200 bg-primary-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          test.id === progressData.currentResult.id
                            ? 'bg-primary-600 text-white'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {new Date(test.testDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                            {test.id === progressData.currentResult.id && (
                              <span className="ml-2 text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full">
                                Current
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">{test.testCenter}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-lg font-bold text-primary-600">
                            {test.overallBandScore || 'N/A'}
                          </div>
                          <div className="text-xs text-muted-foreground">Overall</div>
                        </div>
                        <div className={`p-1 rounded-full ${
                          test.status === 'completed' ? 'bg-success-100' :
                          test.status === 'verified' ? 'bg-blue-100' : 'bg-warning-100'
                        }`}>
                          {test.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4 text-success-600" />
                          ) : test.status === 'verified' ? (
                            <Award className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Clock className="h-4 w-4 text-warning-600" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Module Scores */}
                    <div className="grid grid-cols-4 gap-3">
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <div className="text-sm font-semibold text-blue-600">
                          {test.listeningBandScore || 'N/A'}
                        </div>
                        <div className="text-xs text-blue-500">Listening</div>
                      </div>
                      <div className="text-center p-2 bg-emerald-50 rounded">
                        <div className="text-sm font-semibold text-emerald-600">
                          {test.readingBandScore || 'N/A'}
                        </div>
                        <div className="text-xs text-emerald-500">Reading</div>
                      </div>
                      <div className="text-center p-2 bg-amber-50 rounded">
                        <div className="text-sm font-semibold text-amber-600">
                          {test.writingBandScore || 'N/A'}
                        </div>
                        <div className="text-xs text-amber-500">Writing</div>
                      </div>
                      <div className="text-center p-2 bg-purple-50 rounded">
                        <div className="text-sm font-semibold text-purple-600">
                          {test.speakingBandScore || 'N/A'}
                        </div>
                        <div className="text-xs text-purple-500">Speaking</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
            </>
          )}
        </div>
      </main>
    </div>
  );
}
