'use client';

import React, { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, Target, Zap, Info } from 'lucide-react';

interface ScoreData {
  listening?: number | null;
  reading?: number | null;
  writing?: number | null;
  speaking?: number | null;
  overall?: number | null;
}

interface EnhancedProgressChartProps {
  scores: ScoreData;
  className?: string;
  showPrediction?: boolean;
}

interface TooltipData {
  x: number;
  y: number;
  content: string;
  visible: boolean;
  isPrediction?: boolean;
}

export default function EnhancedProgressChart({
  scores,
  className = '',
  showPrediction = true
}: EnhancedProgressChartProps) {
  const [tooltip, setTooltip] = useState<TooltipData>({
    x: 0,
    y: 0,
    content: '',
    visible: false
  });
  const chartRef = useRef<HTMLDivElement>(null);

  // Mock historical data for demonstration
  const historicalData = [
    { date: 'Jun 2024', overall: 6.5, listening: 6.0, reading: 7.0, writing: 6.5, speaking: 6.0 },
    { date: 'Aug 2024', overall: 7.0, listening: 7.5, reading: 7.5, writing: 6.5, speaking: 6.5 },
    { date: 'Current', overall: scores.overall, listening: scores.listening, reading: scores.reading, writing: scores.writing, speaking: scores.speaking }
  ];

  const modules = [
    { key: 'overall', name: 'Overall', color: '#3B82F6', strokeWidth: 3 },
    { key: 'listening', name: 'Listening', color: '#06B6D4', strokeWidth: 2 },
    { key: 'reading', name: 'Reading', color: '#10B981', strokeWidth: 2 },
    { key: 'writing', name: 'Writing', color: '#F59E0B', strokeWidth: 2 },
    { key: 'speaking', name: 'Speaking', color: '#8B5CF6', strokeWidth: 2 }
  ];

  // AI Prediction Algorithm
  const generatePrediction = (moduleKey: string) => {
    const moduleData = historicalData.map(d => d[moduleKey as keyof typeof d]).filter(score => score !== null) as number[];
    if (moduleData.length < 2) return null;

    // Calculate trend
    const trend = (moduleData[moduleData.length - 1] - moduleData[0]) / (moduleData.length - 1);
    const lastScore = moduleData[moduleData.length - 1];

    // Apply trend with some realistic constraints
    let prediction = lastScore + trend * 0.8; // Moderate the trend

    // Add improvement factor based on current performance
    if (lastScore < 6.5) prediction += 0.3; // More room for improvement
    else if (lastScore < 7.5) prediction += 0.2;
    else prediction += 0.1; // Harder to improve at higher levels

    // Clamp between 3 and 9, round to nearest 0.5
    return Math.max(3, Math.min(9, Math.round(prediction * 2) / 2));
  };

  const chartWidth = 600;
  const chartHeight = 300;
  const padding = { top: 40, right: 80, bottom: 60, left: 60 };
  const dataWidth = chartWidth - padding.left - padding.right;
  const dataHeight = chartHeight - padding.top - padding.bottom;

  const xScale = (index: number) => (index / (historicalData.length)) * dataWidth;
  const yScale = (value: number) => dataHeight - ((value - 3) / 6) * dataHeight;

  const handleMouseMove = (event: React.MouseEvent, content: string, isPrediction = false) => {
    if (!chartRef.current) return;

    const rect = chartRef.current.getBoundingClientRect();
    setTooltip({
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      content,
      visible: true,
      isPrediction
    });
  };

  const handleMouseLeave = () => {
    setTooltip(prev => ({ ...prev, visible: false }));
  };

  return (
    <div className={`card-elevated animate-slide-up ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <LineChart className="h-5 w-5 text-primary-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Score Progression Over Time</h2>
          </div>
          {showPrediction && (
            <div className="flex items-center text-sm text-gray-600">
              <Zap className="h-4 w-4 mr-1 text-yellow-500" />
              Includes AI Prediction
            </div>
          )}
        </div>

        {/* Chart Container */}
        <div className="relative" ref={chartRef}>
          <svg width={chartWidth} height={chartHeight} className="overflow-visible">
            {/* Grid lines */}
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width={dataWidth} height={dataHeight} x={padding.left} y={padding.top} fill="url(#grid)" />

            {/* Y-axis labels */}
            {[3, 4, 5, 6, 7, 8, 9].map(score => (
              <g key={score}>
                <line
                  x1={padding.left}
                  y1={padding.top + yScale(score)}
                  x2={padding.left + dataWidth}
                  y2={padding.top + yScale(score)}
                  stroke="#e5e7eb"
                  strokeWidth="1"
                />
                <text
                  x={padding.left - 10}
                  y={padding.top + yScale(score) + 4}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {score}
                </text>
              </g>
            ))}

            {/* X-axis labels */}
            {historicalData.map((data, index) => (
              <text
                key={index}
                x={padding.left + xScale(index)}
                y={chartHeight - 20}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {data.date}
              </text>
            ))}

            {/* Module lines and points */}
            {modules.map(module => {
              const moduleScores = historicalData.map(d => d[module.key as keyof typeof d]).filter(score => score !== null) as number[];
              if (moduleScores.length < 2) return null;

              // Create path for historical data
              const pathData = moduleScores.map((score, index) =>
                `${index === 0 ? 'M' : 'L'} ${padding.left + xScale(index)} ${padding.top + yScale(score)}`
              ).join(' ');

              const prediction = showPrediction ? generatePrediction(module.key) : null;
              const predictionX = padding.left + xScale(historicalData.length);
              const predictionY = prediction ? padding.top + yScale(prediction) : 0;

              return (
                <g key={module.key}>
                  {/* Historical line */}
                  <path
                    d={pathData}
                    fill="none"
                    stroke={module.color}
                    strokeWidth={module.strokeWidth}
                    className="transition-all duration-300"
                  />

                  {/* Prediction line */}
                  {showPrediction && prediction && (
                    <path
                      d={`M ${padding.left + xScale(historicalData.length - 1)} ${padding.top + yScale(moduleScores[moduleScores.length - 1])} L ${predictionX} ${predictionY}`}
                      fill="none"
                      stroke={module.color}
                      strokeWidth={module.strokeWidth}
                      strokeDasharray="8,4"
                      opacity="0.7"
                      className="transition-all duration-300"
                    />
                  )}

                  {/* Historical data points */}
                  {moduleScores.map((score, index) => (
                    <circle
                      key={index}
                      cx={padding.left + xScale(index)}
                      cy={padding.top + yScale(score)}
                      r={module.key === 'overall' ? 5 : 4}
                      fill={module.color}
                      className="cursor-pointer transition-all duration-200 hover:r-6"
                      onMouseMove={(e) => handleMouseMove(e, `${module.name}: ${score} (${historicalData[index].date})`)}
                      onMouseLeave={handleMouseLeave}
                    />
                  ))}

                  {/* Prediction point */}
                  {showPrediction && prediction && (
                    <g>
                      <circle
                        cx={predictionX}
                        cy={predictionY}
                        r={module.key === 'overall' ? 6 : 5}
                        fill={module.color}
                        fillOpacity="0.6"
                        stroke={module.color}
                        strokeWidth="2"
                        strokeDasharray="4,4"
                        className="animate-pulse cursor-pointer"
                        onMouseMove={(e) => handleMouseMove(e, `${module.name} Prediction: ${prediction} (AI-generated based on your progress trend)`, true)}
                        onMouseLeave={handleMouseLeave}
                      />
                      {module.key === 'overall' && (
                        <>
                          <text
                            x={predictionX}
                            y={predictionY - 15}
                            textAnchor="middle"
                            className="text-xs font-medium fill-gray-700"
                          >
                            {prediction}
                          </text>
                          <text
                            x={predictionX}
                            y={chartHeight - 5}
                            textAnchor="middle"
                            className="text-xs fill-gray-500"
                          >
                            Next Test
                          </text>
                        </>
                      )}
                    </g>
                  )}
                </g>
              );
            })}
          </svg>

          {/* Tooltip */}
          {tooltip.visible && (
            <div
              className={`absolute z-10 px-3 py-2 text-sm rounded-lg shadow-lg pointer-events-none transition-all duration-200 ${
                tooltip.isPrediction
                  ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
                  : 'bg-gray-900 text-white'
              }`}
              style={{
                left: tooltip.x + 10,
                top: tooltip.y - 10,
                transform: 'translateY(-100%)'
              }}
            >
              {tooltip.isPrediction && <Zap className="h-3 w-3 inline mr-1" />}
              {tooltip.content}
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="mt-6 flex flex-wrap justify-center gap-4">
          {modules.map(module => (
            <div key={module.key} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: module.color }}
              />
              <span className="text-sm text-gray-600">{module.name}</span>
            </div>
          ))}
        </div>

        {/* AI Prediction Summary */}
        {showPrediction && (
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <div className="flex items-center mr-3">
                <Target className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-blue-900">AI Score Prediction</h3>
              </div>
              <Info className="h-4 w-4 text-blue-500 mt-0.5" />
            </div>
            <div className="mt-2 space-y-1">
              <p className="text-sm text-blue-700">
                Based on your progress trend, your next overall band score is predicted to be{' '}
                <span className="font-bold text-blue-800">
                  {generatePrediction('overall') || 'N/A'}
                </span>
              </p>
              <p className="text-xs text-blue-600">
                Predictions use AI analysis of your historical performance and improvement patterns.
                Continue practicing to achieve your target score!
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
