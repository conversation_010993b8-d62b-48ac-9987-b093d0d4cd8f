'use client';

import React, { useState, useRef } from 'react';
import { Line<PERSON><PERSON>, Target, Zap } from 'lucide-react';

interface ScoreProgression {
  listening: (number | null)[];
  reading: (number | null)[];
  writing: (number | null)[];
  speaking: (number | null)[];
  overall: (number | null)[];
  dates: string[];
}

interface InteractiveProgressChartProps {
  scoreProgression: ScoreProgression;
  className?: string;
  showPrediction?: boolean;
}

interface TooltipData {
  x: number;
  y: number;
  date: string;
  score: number;
  module: string;
  visible: boolean;
}

export default function InteractiveProgressChart({
  scoreProgression,
  className = '',
  showPrediction = true
}: InteractiveProgressChartProps) {
  const [tooltip, setTooltip] = useState<TooltipData>({
    x: 0,
    y: 0,
    date: '',
    score: 0,
    module: '',
    visible: false
  });
  const [hoveredModule, setHoveredModule] = useState<string | null>(null);
  const chartRef = useRef<HTMLDivElement>(null);

  const modules = [
    { key: 'overall', name: 'Overall', color: '#3B82F6', strokeWidth: 3 },
    { key: 'listening', name: 'Listening', color: '#06B6D4', strokeWidth: 2 },
    { key: 'reading', name: 'Reading', color: '#10B981', strokeWidth: 2 },
    { key: 'writing', name: 'Writing', color: '#F59E0B', strokeWidth: 2 },
    { key: 'speaking', name: 'Speaking', color: '#8B5CF6', strokeWidth: 2 }
  ];

  const chartWidth = 800;
  const chartHeight = 300;
  const padding = { top: 20, right: 60, bottom: 60, left: 60 };
  const innerWidth = chartWidth - padding.left - padding.right;
  const innerHeight = chartHeight - padding.top - padding.bottom;

  // Calculate scales
  const xScale = (index: number) => (index / Math.max(scoreProgression.dates.length - 1, 1)) * innerWidth;
  const yScale = (score: number) => innerHeight - ((score - 3) / 6) * innerHeight; // 3-9 scale

  // Generate prediction for next test
  const generatePrediction = (scores: (number | null)[]) => {
    const validScores = scores.filter(s => s !== null) as number[];
    if (validScores.length < 2) return null;

    // Simple linear regression for trend
    const n = validScores.length;
    const sumX = validScores.reduce((sum, _, i) => sum + i, 0);
    const sumY = validScores.reduce((sum, score) => sum + score, 0);
    const sumXY = validScores.reduce((sum, score, i) => sum + i * score, 0);
    const sumXX = validScores.reduce((sum, _, i) => sum + i * i, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const nextScore = slope * n + intercept;

    // Clamp between 3 and 9, and add some realistic variation
    return Math.max(3, Math.min(9, Math.round(nextScore * 2) / 2));
  };

  const handleMouseMove = (event: React.MouseEvent, moduleKey: string, index: number) => {
    if (!chartRef.current) return;

    const rect = chartRef.current.getBoundingClientRect();
    const score = scoreProgression[moduleKey as keyof ScoreProgression][index] as number;

    if (score === null) return;

    setTooltip({
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
      date: scoreProgression.dates[index],
      score,
      module: modules.find(m => m.key === moduleKey)?.name || moduleKey,
      visible: true
    });
    setHoveredModule(moduleKey);
  };

  const handleMouseLeave = () => {
    setTooltip(prev => ({ ...prev, visible: false }));
    setHoveredModule(null);
  };

  // Generate SVG path for a line
  const generatePath = (scores: (number | null)[]) => {
    const points = scores
      .map((score, index) => score !== null ? `${xScale(index)},${yScale(score)}` : null)
      .filter(point => point !== null);

    if (points.length === 0) return '';

    return `M ${points.join(' L ')}`;
  };

  return (
    <div className={`card-elevated animate-slide-up ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <LineChart className="h-5 w-5 text-primary-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Score Progression Over Time</h2>
          </div>
          {showPrediction && (
            <div className="flex items-center text-sm text-gray-600">
              <Zap className="h-4 w-4 mr-1 text-yellow-500" />
              Includes AI Prediction
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="flex flex-wrap gap-4 mb-6">
          {modules.map(module => (
            <div
              key={module.key}
              className={`flex items-center cursor-pointer transition-opacity duration-200 ${
                hoveredModule && hoveredModule !== module.key ? 'opacity-40' : 'opacity-100'
              }`}
              onMouseEnter={() => setHoveredModule(module.key)}
              onMouseLeave={() => setHoveredModule(null)}
            >
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: module.color }}
              />
              <span className="text-sm font-medium text-gray-700">{module.name}</span>
            </div>
          ))}
        </div>

        {/* Chart Container */}
        <div className="relative bg-gray-50 rounded-lg p-4" ref={chartRef}>
          <svg width={chartWidth} height={chartHeight} className="overflow-visible">
            {/* Grid lines */}
            <defs>
              <pattern id="grid" width="40" height="30" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 30" fill="none" stroke="#E5E7EB" strokeWidth="1" opacity="0.5"/>
              </pattern>
            </defs>
            <rect width={innerWidth} height={innerHeight} x={padding.left} y={padding.top} fill="url(#grid)" />

            {/* Y-axis labels */}
            {[3, 4.5, 6, 7.5, 9].map(score => (
              <g key={score}>
                <text
                  x={padding.left - 10}
                  y={padding.top + yScale(score) + 4}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {score}
                </text>
                <line
                  x1={padding.left}
                  y1={padding.top + yScale(score)}
                  x2={padding.left + innerWidth}
                  y2={padding.top + yScale(score)}
                  stroke="#E5E7EB"
                  strokeWidth="1"
                  opacity="0.3"
                />
              </g>
            ))}

            {/* X-axis labels */}
            {scoreProgression.dates.map((date, index) => (
              <text
                key={index}
                x={padding.left + xScale(index)}
                y={chartHeight - 20}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {date}
              </text>
            ))}

            {/* Score lines */}
            {modules.map(module => {
              const scores = scoreProgression[module.key as keyof ScoreProgression] as (number | null)[];
              const path = generatePath(scores);

              if (!path) return null;

              return (
                <g key={module.key}>
                  <path
                    d={path}
                    fill="none"
                    stroke={module.color}
                    strokeWidth={module.strokeWidth}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={`transition-all duration-300 ${
                      hoveredModule && hoveredModule !== module.key ? 'opacity-30' : 'opacity-100'
                    }`}
                    transform={`translate(${padding.left}, ${padding.top})`}
                  />

                  {/* Data points */}
                  {scores.map((score, index) => {
                    if (score === null) return null;

                    return (
                      <circle
                        key={index}
                        cx={padding.left + xScale(index)}
                        cy={padding.top + yScale(score)}
                        r={module.key === 'overall' ? 5 : 4}
                        fill={module.color}
                        className={`cursor-pointer transition-all duration-200 hover:r-6 ${
                          hoveredModule && hoveredModule !== module.key ? 'opacity-30' : 'opacity-100'
                        }`}
                        onMouseMove={(e) => handleMouseMove(e, module.key, index)}
                        onMouseLeave={handleMouseLeave}
                      />
                    );
                  })}

                  {/* Prediction point */}
                  {showPrediction && module.key === 'overall' && (
                    (() => {
                      const prediction = generatePrediction(scores);
                      if (!prediction) return null;

                      const nextIndex = scores.length;
                      return (
                        <g>
                          <circle
                            cx={padding.left + xScale(nextIndex)}
                            cy={padding.top + yScale(prediction)}
                            r="6"
                            fill={module.color}
                            fillOpacity="0.6"
                            stroke={module.color}
                            strokeWidth="2"
                            strokeDasharray="4,4"
                            className="animate-pulse"
                          />
                          <text
                            x={padding.left + xScale(nextIndex)}
                            y={padding.top + yScale(prediction) - 15}
                            textAnchor="middle"
                            className="text-xs font-medium fill-gray-700"
                          >
                            {prediction}
                          </text>
                          <text
                            x={padding.left + xScale(nextIndex)}
                            y={chartHeight - 5}
                            textAnchor="middle"
                            className="text-xs fill-gray-500"
                          >
                            Next Test
                          </text>
                        </g>
                      );
                    })()
                  )}
                </g>
              );
            })}
          </svg>

          {/* Tooltip */}
          {tooltip.visible && (
            <div
              className="absolute z-10 bg-white border border-gray-200 rounded-lg shadow-lg p-3 pointer-events-none transform -translate-x-1/2 -translate-y-full"
              style={{
                left: tooltip.x,
                top: tooltip.y - 10
              }}
            >
              <div className="text-sm font-medium text-gray-900">{tooltip.module}</div>
              <div className="text-sm text-gray-600">{tooltip.date}</div>
              <div className="text-lg font-bold text-primary-600">{tooltip.score}</div>
            </div>
          )}
        </div>

        {/* Prediction Summary */}
        {showPrediction && (
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
            <div className="flex items-center mb-2">
              <Target className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="font-medium text-blue-900">AI Score Prediction</h3>
            </div>
            <p className="text-sm text-blue-700">
              Based on your progress trend, your next overall band score is predicted to be{' '}
              <span className="font-bold">
                {generatePrediction(scoreProgression.overall) || 'N/A'}
              </span>
              . Keep practicing to achieve your target score!
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
