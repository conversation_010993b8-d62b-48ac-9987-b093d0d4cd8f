'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Minus, BarChart3, Target, Award, Users } from 'lucide-react';

interface PerformanceMetrics {
  averageScore: number | null;
  highestScore: number | null;
  lowestScore: number | null;
  scoreDistribution: {
    listening?: number | null;
    reading?: number | null;
    writing?: number | null;
    speaking?: number | null;
  };
}

interface PerformanceChartProps {
  metrics: PerformanceMetrics;
  overallScore?: number | null;
  className?: string;
}

export default function PerformanceChart({
  metrics,
  overallScore,
  className = ''
}: PerformanceChartProps) {
  const globalAverage = 6.5; // IELTS global average

  const getPerformanceIndicator = (score: number | null, average: number) => {
    if (!score) return {
      icon: Minus,
      color: 'text-gray-400',
      bgColor: 'bg-gray-50',
      text: 'No Score',
      badge: 'badge-info'
    };

    const difference = score - average;
    if (difference > 1) {
      return {
        icon: TrendingUp,
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-50',
        text: 'Excellent',
        badge: 'badge-success'
      };
    } else if (difference > 0) {
      return {
        icon: TrendingUp,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        text: 'Above Average',
        badge: 'badge-success'
      };
    } else if (difference < -1) {
      return {
        icon: TrendingDown,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        text: 'Below Average',
        badge: 'badge-error'
      };
    } else if (difference < 0) {
      return {
        icon: TrendingDown,
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        text: 'Slightly Below',
        badge: 'badge-warning'
      };
    } else {
      return {
        icon: Minus,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        text: 'Average',
        badge: 'badge-info'
      };
    }
  };

  const overallIndicator = getPerformanceIndicator(overallScore ?? null, globalAverage);

  return (
    <div className={`card-elevated animate-fade-in ${className}`}>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="p-2 bg-primary-100 rounded-lg mr-3">
            <BarChart3 className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Performance Analysis</h3>
            <p className="text-sm text-muted-foreground">Compare with global benchmarks</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Overall Performance */}
          <div className={`rounded-xl p-5 ${overallIndicator.bgColor} border border-transparent animate-slide-up`}>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <Target className={`h-5 w-5 mr-2 ${overallIndicator.color}`} />
                <h4 className="text-sm font-semibold text-gray-800">Overall Performance</h4>
              </div>
              <span className={`badge ${overallIndicator.badge}`}>
                {overallIndicator.text}
              </span>
            </div>
            <div className="flex items-baseline space-x-2 mb-2">
              <span className="text-3xl font-bold text-gray-900">
                {overallScore || '--'}
              </span>
              <span className="text-sm text-muted-foreground">/9</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Global Average:</span>
              <span className="font-medium text-gray-700">{globalAverage}</span>
            </div>
            <div className="mt-3">
              <div className="flex items-center">
                <overallIndicator.icon className={`h-4 w-4 mr-1 ${overallIndicator.color}`} />
                <span className={`text-sm font-medium ${overallIndicator.color}`}>
                  {overallScore ? `${overallScore > globalAverage ? '+' : ''}${(overallScore - globalAverage).toFixed(1)} vs global` : 'No comparison available'}
                </span>
              </div>
            </div>
          </div>

          {/* Score Range */}
          <div className="rounded-xl p-5 bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-100 animate-slide-up" style={{ animationDelay: '150ms' }}>
            <div className="flex items-center mb-3">
              <Award className="h-5 w-5 mr-2 text-emerald-600" />
              <h4 className="text-sm font-semibold text-gray-800">Score Range</h4>
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-xl font-bold text-emerald-600">
                  {metrics.highestScore || '--'}
                </div>
                <div className="text-xs text-emerald-700 font-medium">Highest</div>
              </div>
              <div>
                <div className="text-xl font-bold text-blue-600">
                  {metrics.averageScore ? metrics.averageScore.toFixed(1) : '--'}
                </div>
                <div className="text-xs text-blue-700 font-medium">Average</div>
              </div>
              <div>
                <div className="text-xl font-bold text-amber-600">
                  {metrics.lowestScore || '--'}
                </div>
                <div className="text-xs text-amber-700 font-medium">Lowest</div>
              </div>
            </div>
            {metrics.highestScore && metrics.lowestScore && (
              <div className="mt-3 pt-3 border-t border-emerald-200">
                <div className="text-center">
                  <span className="text-sm text-emerald-700">
                    Consistency: <span className="font-semibold">
                      {(metrics.highestScore - metrics.lowestScore).toFixed(1)} band spread
                    </span>
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Module Performance Comparison */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <Users className="h-5 w-5 mr-2 text-primary-600" />
            <h4 className="text-sm font-semibold text-gray-800">Module Performance vs Global Average</h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(metrics.scoreDistribution)
              .filter(([, score]) => score !== null && score !== undefined)
              .map(([module, score], index) => {
              const indicator = getPerformanceIndicator(score ?? null, globalAverage);
              return (
                <div
                  key={module}
                  className={`text-center p-4 rounded-xl border transition-all duration-200 hover:shadow-md animate-slide-up ${indicator.bgColor} border-gray-200`}
                  style={{ animationDelay: `${300 + index * 100}ms` }}
                >
                  <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2">
                    {module}
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-2">
                    {score}
                  </div>
                  <div className="flex items-center justify-center mb-2">
                    <indicator.icon className={`h-4 w-4 ${indicator.color}`} />
                  </div>
                  <span className={`badge ${indicator.badge} text-xs`}>
                    {indicator.text}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="rounded-xl p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 animate-slide-up" style={{ animationDelay: '700ms' }}>
          <h4 className="text-sm font-semibold text-blue-800 mb-3 flex items-center">
            <Target className="h-4 w-4 mr-2" />
            Performance Insights
          </h4>
          <div className="space-y-2 text-sm text-blue-700">
            {overallScore && overallScore >= 8 && (
              <div className="flex items-start">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p>Outstanding performance! You&apos;re significantly above the global average and demonstrate expert-level English proficiency.</p>
              </div>
            )}
            {overallScore && overallScore >= 7 && overallScore < 8 && (
              <div className="flex items-start">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p>Excellent performance! You&apos;re well above the global average with very good English proficiency.</p>
              </div>
            )}
            {overallScore && overallScore >= 6 && overallScore < 7 && (
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p>Good performance! You&apos;re close to or at the global average, showing competent English skills.</p>
              </div>
            )}
            {overallScore && overallScore < 6 && (
              <div className="flex items-start">
                <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p>There&apos;s room for improvement. Focus on your weaker modules to boost your overall performance.</p>
              </div>
            )}
            {metrics.highestScore && metrics.lowestScore && (
              <div className="flex items-start">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p>
                  Score consistency: {(metrics.highestScore - metrics.lowestScore) <= 1 ? 'Very consistent' :
                    (metrics.highestScore - metrics.lowestScore) <= 2 ? 'Moderately consistent' : 'Variable'}
                  performance across modules ({(metrics.highestScore - metrics.lowestScore).toFixed(1)} band difference).
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
