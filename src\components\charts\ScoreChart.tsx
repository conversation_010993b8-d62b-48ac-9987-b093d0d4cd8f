'use client';

import React from 'react';
import { Headphones, Eye, PenTool, MessageCircle, Trophy } from 'lucide-react';

interface ScoreData {
  listening?: number | null;
  reading?: number | null;
  writing?: number | null;
  speaking?: number | null;
  overall?: number | null;
}

interface ScoreChartProps {
  scores: ScoreData;
  className?: string;
}

export default function ScoreChart({ scores, className = '' }: ScoreChartProps) {
  const modules = [
    {
      name: 'Listening',
      score: scores.listening,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      icon: Headphones
    },
    {
      name: 'Reading',
      score: scores.reading,
      color: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50',
      textColor: 'text-emerald-700',
      icon: Eye
    },
    {
      name: 'Writing',
      score: scores.writing,
      color: 'bg-gradient-to-r from-amber-500 to-amber-600',
      bgColor: 'bg-amber-50',
      textColor: 'text-amber-700',
      icon: PenTool
    },
    {
      name: 'Speaking',
      score: scores.speaking,
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      icon: MessageCircle
    },
  ].filter(module => module.score !== null && module.score !== undefined);

  const maxScore = 9;

  const getScoreLevel = (score: number) => {
    if (score >= 8.5) return { level: 'Expert', color: 'text-emerald-600' };
    if (score >= 7.5) return { level: 'Very Good', color: 'text-blue-600' };
    if (score >= 6.5) return { level: 'Good', color: 'text-amber-600' };
    if (score >= 5.5) return { level: 'Modest', color: 'text-orange-600' };
    return { level: 'Limited', color: 'text-red-600' };
  };

  return (
    <div className={`card-elevated animate-fade-in ${className}`}>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="p-2 bg-primary-100 rounded-lg mr-3">
            <Trophy className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Score Breakdown</h3>
            <p className="text-sm text-muted-foreground">Individual module performance</p>
          </div>
        </div>

        <div className="space-y-6">
          {modules.map((module, index) => {
            const Icon = module.icon;
            const scoreLevel = getScoreLevel(module.score || 0);

            return (
              <div
                key={module.name}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg mr-3 ${module.bgColor}`}>
                      <Icon className={`h-4 w-4 ${module.textColor}`} />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-900">{module.name}</span>
                      <div className={`text-xs ${scoreLevel.color} font-medium`}>
                        {scoreLevel.level}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold text-gray-900">
                      {module.score}
                    </span>
                    <span className="text-sm text-muted-foreground ml-1">/9</span>
                  </div>
                </div>

                <div className="relative">
                  <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden">
                    <div
                      className={`h-full rounded-full ${module.color} transition-all duration-1000 ease-out shadow-sm`}
                      style={{
                        width: module.score ? `${(module.score / maxScore) * 100}%` : '0%',
                        animationDelay: `${index * 200 + 500}ms`
                      }}
                    />
                  </div>
                  {/* Score markers */}
                  <div className="absolute top-0 left-0 w-full h-3 flex justify-between items-center px-1">
                    {[...Array(9)].map((_, i) => (
                      <div
                        key={i}
                        className="w-px h-1 bg-white/50"
                        style={{ marginLeft: i === 0 ? '0' : 'auto' }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Overall Score */}
          {scores.overall && (
            <div className="mt-8 pt-6 border-t border-border animate-slide-up" style={{ animationDelay: '800ms' }}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="p-3 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl mr-4 shadow-lg">
                    <Trophy className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <span className="text-lg font-semibold text-gray-900">Overall Band Score</span>
                    <div className={`text-sm font-medium ${getScoreLevel(scores.overall).color}`}>
                      {getScoreLevel(scores.overall).level} User
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-4xl font-bold text-primary-600">
                    {scores.overall}
                  </span>
                  <span className="text-lg text-muted-foreground ml-1">/9</span>
                </div>
              </div>

              <div className="relative">
                <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden shadow-inner">
                  <div
                    className="h-full rounded-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-1000 ease-out shadow-sm"
                    style={{
                      width: `${(scores.overall / maxScore) * 100}%`,
                      animationDelay: '1000ms'
                    }}
                  />
                </div>
                {/* Enhanced score markers for overall */}
                <div className="absolute top-0 left-0 w-full h-4 flex justify-between items-center px-1">
                  {[...Array(9)].map((_, i) => (
                    <div
                      key={i}
                      className="w-px h-2 bg-white/60"
                      style={{ marginLeft: i === 0 ? '0' : 'auto' }}
                    />
                  ))}
                </div>
              </div>

              {/* Score scale reference */}
              <div className="mt-3 flex justify-between text-xs text-muted-foreground">
                <span>1</span>
                <span>3</span>
                <span>5</span>
                <span>7</span>
                <span>9</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
