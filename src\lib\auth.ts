import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { db } from './db';
import { users } from './db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

export const { handlers, auth, signIn, signOut } = NextAuth({
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        console.log('🔐 Authorization attempt:', { email: credentials?.email });

        if (!credentials?.email || !credentials?.password) {
          console.log('❌ Missing credentials');
          return null;
        }

        // Demo credentials for Vercel deployment
        const demoUsers = [
          {
            id: 'admin-1',
            email: '<EMAIL>',
            password: 'admin123',
            name: 'Admin User',
            role: 'admin'
          },
          {
            id: 'checker-1',
            email: '<EMAIL>',
            password: 'checker123',
            name: 'Test Checker',
            role: 'test_checker'
          }
        ];

        // Check demo credentials first
        const demoUser = demoUsers.find(user =>
          user.email === credentials.email && user.password === credentials.password
        );

        if (demoUser) {
          console.log('✅ Demo user authenticated:', { email: demoUser.email, role: demoUser.role });
          return {
            id: demoUser.id,
            email: demoUser.email,
            name: demoUser.name,
            role: demoUser.role,
          };
        }

        // Fallback to database authentication for other users
        try {
          const foundUser = await db
            .select()
            .from(users)
            .where(eq(users.email, credentials.email as string))
            .limit(1);

          if (foundUser.length === 0) {
            return null;
          }

          const user = foundUser[0];

          if (!user.password) {
            return null;
          }

          const isValidPassword = await bcrypt.compare(
            credentials.password as string,
            user.password
          );

          if (!isValidPassword) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
          };
        } catch (error) {
          console.error('Database authentication error:', error);
          // Return null if database fails, but demo users should still work
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        console.log('🎫 JWT callback - user data:', { id: user.id, email: user.email, role: user.role });
        token.id = user.id;
        token.role = user.role;
        token.email = user.email;
        token.name = user.name;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        console.log('📋 Session callback - token data:', { id: token.id, email: token.email, role: token.role });
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
      }
      console.log('📋 Final session:', { user: session.user });
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle redirects after sign in
      console.log('🔄 Redirect callback:', { url, baseUrl });

      // If it's a relative URL, make it absolute
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }

      // If it's the same origin, allow it
      if (new URL(url).origin === baseUrl) {
        return url;
      }

      // Default to base URL
      return baseUrl;
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
  debug: process.env.NODE_ENV === 'development',
  trustHost: true, // Important for Vercel deployment
});
