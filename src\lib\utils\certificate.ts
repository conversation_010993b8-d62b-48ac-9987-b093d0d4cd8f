/**
 * Certificate utility functions for IELTS certification system
 */

/**
 * Generate a unique certificate serial number
 * Format: IELTS-YYYY-NNNNNN (e.g., IELTS-2024-123456)
 */
export function generateCertificateSerial(): string {
  const year = new Date().getFullYear();
  const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
  return `IELTS-${year}-${randomNumber}`;
}

/**
 * Validate certificate serial number format
 */
export function validateCertificateSerial(serial: string): boolean {
  const pattern = /^IELTS-\d{4}-\d{6}$/;
  return pattern.test(serial);
}

/**
 * Extract year from certificate serial
 */
export function extractYearFromSerial(serial: string): number | null {
  const match = serial.match(/^IELTS-(\d{4})-\d{6}$/);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * Format certificate serial for display
 */
export function formatCertificateSerial(serial: string): string {
  if (!validateCertificateSerial(serial)) {
    return serial;
  }
  
  // Add spaces for better readability: IELTS-2024-123456 -> IELTS 2024 123456
  return serial.replace(/-/g, ' ');
}
