/**
 * Utility functions for date handling in the IELTS system
 * Since IELTS tests are held every Sunday, these functions help with Sunday-based date calculations
 */

/**
 * Get the most recent Sunday (including today if it's Sunday)
 */
export function getMostRecentSunday(date: Date = new Date()): Date {
  const result = new Date(date);
  const dayOfWeek = result.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  
  if (dayOfWeek === 0) {
    // Today is Sunday, return today
    return result;
  } else {
    // Go back to the most recent Sunday
    result.setDate(result.getDate() - dayOfWeek);
    return result;
  }
}

/**
 * Get the next Sunday from the given date
 */
export function getNextSunday(date: Date = new Date()): Date {
  const result = new Date(date);
  const dayOfWeek = result.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  
  if (dayOfWeek === 0) {
    // Today is Sunday, get next Sunday
    result.setDate(result.getDate() + 7);
  } else {
    // Get the next Sunday
    result.setDate(result.getDate() + (7 - dayOfWeek));
  }
  
  return result;
}

/**
 * Get the upcoming Sunday (next Sunday if today is not Sunday, today if it is Sunday)
 */
export function getUpcomingSunday(date: Date = new Date()): Date {
  const result = new Date(date);
  const dayOfWeek = result.getDay();
  
  if (dayOfWeek === 0) {
    // Today is Sunday, return today
    return result;
  } else {
    // Get the next Sunday
    return getNextSunday(date);
  }
}

/**
 * Get a list of recent and upcoming Sundays for test date selection
 * Returns an array of Sunday dates, including past and future Sundays
 */
export function getTestDateOptions(weeksBack: number = 4, weeksForward: number = 8): Date[] {
  const today = new Date();
  const sundays: Date[] = [];
  
  // Get the most recent Sunday as our starting point
  const startSunday = getMostRecentSunday(today);
  
  // Add past Sundays
  for (let i = weeksBack; i > 0; i--) {
    const pastSunday = new Date(startSunday);
    pastSunday.setDate(startSunday.getDate() - (i * 7));
    sundays.push(pastSunday);
  }
  
  // Add current Sunday (if it's Sunday) or the most recent Sunday
  sundays.push(startSunday);
  
  // Add future Sundays
  for (let i = 1; i <= weeksForward; i++) {
    const futureSunday = new Date(startSunday);
    futureSunday.setDate(startSunday.getDate() + (i * 7));
    sundays.push(futureSunday);
  }
  
  return sundays;
}

/**
 * Format a date for display in the UI
 */
export function formatTestDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format a date for HTML date input (YYYY-MM-DD)
 */
export function formatDateForInput(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Check if a given date is a Sunday
 */
export function isSunday(date: Date): boolean {
  return date.getDay() === 0;
}

/**
 * Get the default test date for Quick Entry (most recent or upcoming Sunday)
 */
export function getDefaultQuickEntryDate(): Date {
  const today = new Date();
  const dayOfWeek = today.getDay();
  
  // If it's Sunday, Monday, or Tuesday, use the most recent Sunday
  // If it's Wednesday through Saturday, use the upcoming Sunday
  if (dayOfWeek <= 2) {
    return getMostRecentSunday(today);
  } else {
    return getUpcomingSunday(today);
  }
}
