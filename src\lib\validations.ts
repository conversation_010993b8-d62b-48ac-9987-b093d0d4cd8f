import { z } from 'zod';

// Candidate registration schema
export const candidateSchema = z.object({
  candidateNumber: z.string().min(3, 'Candidate number must be at least 3 characters').optional(),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 characters'),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
  }),
  nationality: z.string().min(2, 'Nationality is required'),
  passportNumber: z.string().min(6, 'Passport number must be at least 6 characters'),
  testDate: z.date({
    required_error: 'Test date is required',
  }),
  testCenter: z.string().min(2, 'Test center is required'),
});

// Test result entry schema
export const testResultSchema = z.object({
  candidateId: z.string().min(1, 'Candidate ID is required'),

  // Listening scores
  listeningScore: z.number().min(0).max(40).optional(),
  listeningBandScore: z.number().min(0).max(9).step(0.5).optional(),

  // Reading scores
  readingScore: z.number().min(0).max(40).optional(),
  readingBandScore: z.number().min(0).max(9).step(0.5).optional(),

  // Writing scores
  writingTask1Score: z.number().min(0).max(9).step(0.5).optional(),
  writingTask2Score: z.number().min(0).max(9).step(0.5).optional(),
  writingBandScore: z.number().min(0).max(9).step(0.5).optional(),

  // Speaking scores
  speakingFluencyScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5).optional(),
  speakingBandScore: z.number().min(0).max(9).step(0.5).optional(),

  // Overall score
  overallBandScore: z.number().min(0).max(9).step(0.5).optional(),
});

// Search schema
export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  searchType: z.enum(['name', 'email', 'passport', 'certificate']),
});

// Login schema
export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

// User creation schema
export const userSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  role: z.enum(['admin', 'test_checker']),
});

export type CandidateFormData = z.infer<typeof candidateSchema>;
export type TestResultFormData = z.infer<typeof testResultSchema>;
export type SearchFormData = z.infer<typeof searchSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
export type UserFormData = z.infer<typeof userSchema>;
