import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export default function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  console.log('🛡️ Middleware check for:', pathname);

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/search',
    '/auth/signin',
    '/progress',
    '/feedback',
    '/certification'
  ];

  // Public route patterns (using regex for dynamic routes)
  const publicRoutePatterns = [
    /^\/results\/[^\/]+$/, // /results/[id]
    /^\/results\/[^\/]+\/progress$/, // /results/[id]/progress
    /^\/results\/[^\/]+\/feedback$/, // /results/[id]/feedback
    /^\/results\/[^\/]+\/certificate$/, // /results/[id]/certificate
    /^\/verify(\/[^\/]+)?$/, // /verify and /verify/[serial]
    /^\/api\//, // All API routes
    /^\/auth\//, // All auth routes
    /^\/_next\//, // Next.js internal routes
    /^\/favicon\.ico$/, // Favicon
  ];

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    console.log('✅ Public route, allowing access');
    return NextResponse.next();
  }

  // Allow public route patterns
  if (publicRoutePatterns.some(pattern => pattern.test(pathname))) {
    console.log('✅ Public route pattern, allowing access');
    return NextResponse.next();
  }

  // For protected routes, check authentication
  const protectedRoutes = ['/admin', '/dashboard'];

  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    console.log('🔒 Protected route detected');

    // Check for session cookies with multiple possible names
    const sessionCookies = [
      'next-auth.session-token',
      '__Secure-next-auth.session-token',
      'authjs.session-token',
      '__Secure-authjs.session-token'
    ];

    const sessionCookie = sessionCookies
      .map(name => req.cookies.get(name))
      .find(cookie => cookie !== undefined);

    if (!sessionCookie) {
      console.log('❌ No session cookie found, redirecting to signin');
      const signInUrl = new URL('/auth/signin', req.url);
      signInUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(signInUrl);
    }

    console.log('✅ Session cookie found, allowing access');
  }

  console.log('✅ Allowing access to:', pathname);
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
