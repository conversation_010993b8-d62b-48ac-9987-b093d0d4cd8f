// Test script to verify candidate number generation for different test dates
// This script tests the candidate registration API to ensure:
// 1. Candidate numbers are scoped to test dates
// 2. Existing candidates can register for new test dates
// 3. No conflicts occur when the same candidate registers for different dates

const testCandidateRegistration = async () => {
  const baseUrl = 'http://localhost:3001';
  
  // Test data
  const testCandidate = {
    fullName: 'Test User',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
    dateOfBirth: '1990-01-01',
    nationality: 'Test Country',
    passportNumber: 'TEST123456',
    testCenter: 'Innovative Centre - Samarkand'
  };

  const testDate1 = '2025-01-12'; // Sunday
  const testDate2 = '2025-01-19'; // Next Sunday

  try {
    console.log('🧪 Testing candidate registration with scoped candidate numbers...\n');

    // Test 1: Register candidate for first test date
    console.log('📝 Test 1: Registering candidate for first test date...');
    const response1 = await fetch(`${baseUrl}/api/admin/candidates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...testCandidate,
        testDate: testDate1
      })
    });

    if (response1.ok) {
      const result1 = await response1.json();
      console.log(`✅ Success: Candidate registered with number ${result1.candidateNumber} for ${testDate1}`);
    } else {
      const error1 = await response1.json();
      console.log(`❌ Failed: ${error1.error}`);
      return;
    }

    // Test 2: Register same candidate for second test date
    console.log('\n📝 Test 2: Registering same candidate for second test date...');
    const response2 = await fetch(`${baseUrl}/api/admin/candidates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...testCandidate,
        testDate: testDate2
      })
    });

    if (response2.ok) {
      const result2 = await response2.json();
      console.log(`✅ Success: Same candidate registered with number ${result2.candidateNumber} for ${testDate2}`);
    } else {
      const error2 = await response2.json();
      console.log(`❌ Failed: ${error2.error}`);
      return;
    }

    // Test 3: Try to register same candidate for same test date (should fail)
    console.log('\n📝 Test 3: Trying to register same candidate for same test date (should fail)...');
    const response3 = await fetch(`${baseUrl}/api/admin/candidates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...testCandidate,
        testDate: testDate1 // Same as first registration
      })
    });

    if (!response3.ok) {
      const error3 = await response3.json();
      console.log(`✅ Expected failure: ${error3.error}`);
    } else {
      console.log(`❌ Unexpected success: Duplicate registration should have failed`);
    }

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
};

// Note: This is a manual test script. To run it, you would need to:
// 1. Ensure the development server is running
// 2. Have proper authentication setup
// 3. Run this script with Node.js
console.log('This is a test script template. Actual testing should be done through the UI or with proper authentication.');
