#!/usr/bin/env node

const baseUrl = 'http://localhost:3001';

async function testEndpoint(url, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📍 URL: ${url}`);
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    
    if (response.ok) {
      console.log('✅ Success');
      if (data.candidateId) {
        console.log(`   Candidate: ${data.fullName}`);
        console.log(`   Total Tests: ${data.totalTests}`);
        console.log(`   Average Score: ${data.averageScore}`);
      } else if (data.candidate) {
        console.log(`   Candidate: ${data.candidate.fullName}`);
        console.log(`   Overall Score: ${data.overallBandScore}`);
      }
    } else {
      console.log(`❌ Error: ${data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
  }
}

async function testProgressAPI() {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 TESTING PROGRESS API IMPROVEMENTS');
  console.log('='.repeat(60));

  // Test with the known working result ID
  const testResultId = 'zwrt4hgu9lnqa9ojg1m5j65n';
  
  // First get the result to extract candidate ID
  console.log('\n📋 Step 1: Getting test result details...');
  try {
    const resultResponse = await fetch(`${baseUrl}/api/results/${testResultId}`);
    
    if (resultResponse.ok) {
      const resultData = await resultResponse.json();
      console.log('✅ Test result found');
      console.log(`   Candidate: ${resultData.candidate.fullName}`);
      console.log(`   Score: ${resultData.overallBandScore}`);
      
      const candidateId = resultData.candidate.id;
      
      // Test the new progress API endpoint
      await testEndpoint(
        `${baseUrl}/api/candidates/${candidateId}/progress`,
        'New Progress API Endpoint'
      );
      
    } else {
      console.log('❌ Test result not found, testing with mock candidate ID');
      await testEndpoint(
        `${baseUrl}/api/candidates/test-candidate-id/progress`,
        'Progress API with Mock ID (should return 404)'
      );
    }
  } catch (error) {
    console.log(`❌ Error testing result: ${error.message}`);
  }
}

async function testPageAccess() {
  console.log('\n' + '='.repeat(60));
  console.log('🌐 TESTING PAGE ACCESS');
  console.log('='.repeat(60));

  const testResultId = 'zwrt4hgu9lnqa9ojg1m5j65n';
  
  const pages = [
    { path: `/results/${testResultId}`, name: 'Overview Page' },
    { path: `/results/${testResultId}/progress`, name: 'Progress Page (with improvements)' },
    { path: `/results/${testResultId}/feedback`, name: 'Feedback Page (horizontal nav)' },
    { path: `/results/${testResultId}/certificate`, name: 'Certificate Page (horizontal nav)' }
  ];

  for (const page of pages) {
    console.log(`\n🔗 Testing: ${page.name}`);
    console.log(`📍 URL: ${baseUrl}${page.path}`);
    
    try {
      const response = await fetch(`${baseUrl}${page.path}`);
      
      if (response.ok) {
        const html = await response.text();
        
        // Check for key improvements
        const hasHorizontalNav = html.includes('HorizontalNavigationMenu') || 
                                html.includes('flex space-x-1 py-4 overflow-x-auto');
        const hasInteractiveChart = html.includes('InteractiveProgressChart') ||
                                  html.includes('Score Progression Over Time');
        const hasSingleTestMotivation = html.includes('SingleTestMotivation') ||
                                      html.includes('Welcome to Your IELTS Journey');
        
        console.log('✅ Page loads successfully');
        console.log(`   Horizontal Navigation: ${hasHorizontalNav ? '✅' : '❌'}`);
        
        if (page.path.includes('/progress')) {
          console.log(`   Interactive Chart: ${hasInteractiveChart ? '✅' : '❌'}`);
          console.log(`   Single Test Motivation: ${hasSingleTestMotivation ? '✅' : '❌'}`);
        }
        
      } else {
        console.log(`❌ Page failed to load: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Network Error: ${error.message}`);
    }
  }
}

async function runAllTests() {
  console.log('🚀 IELTS CERTIFICATION SYSTEM - IMPROVEMENT TESTS');
  console.log('=' .repeat(80));
  console.log('Testing the 4-phase improvements:');
  console.log('1. Layout and Navigation Changes');
  console.log('2. Data Integration Fixes');
  console.log('3. User Experience Improvements');
  console.log('4. Interactive Chart Enhancements');
  console.log('=' .repeat(80));

  await testProgressAPI();
  await testPageAccess();

  console.log('\n' + '='.repeat(60));
  console.log('🏁 ALL TESTS COMPLETED');
  console.log('='.repeat(60));
  console.log('\n📋 Summary of Improvements:');
  console.log('✅ Horizontal navigation implemented on Feedback and Certificate pages');
  console.log('✅ New Progress API endpoint created for real historical data');
  console.log('✅ Interactive progress chart with hover effects and predictions');
  console.log('✅ Single-test motivation component for new candidates');
  console.log('✅ Consistent layout across all result pages');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Test with real candidate data that has multiple test results');
  console.log('2. Verify responsive design on different screen sizes');
  console.log('3. Test interactive chart features (hover, tooltips, predictions)');
  console.log('4. Validate cross-browser compatibility');
}

// Run the tests
runAllTests().catch(console.error);
